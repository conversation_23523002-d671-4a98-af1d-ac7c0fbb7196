# 🚀 PasaBuy Pal - Appwrite Deployment Guide

## Overview

This guide covers deploying PasaBuy Pal to Appwrite Cloud with proper build configuration.

## 🛠️ Build Settings for Appwrite Console

When setting up your deployment in the Appwrite Console, use these exact settings:

### Build Configuration
```
Root Directory: .
Install Command: npm install
Build Command: npm run build
Output Directory: .next
Node Version: 20.x
```

### Environment Variables
Set these in your Appwrite Console under Project Settings > Environment Variables:

```
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your-project-id
APPWRITE_API_KEY=your-api-key
APPWRITE_DATABASE_ID=pasabuy_pal_db
APPWRITE_STORAGE_BUCKET_ID=product-images
NEXT_PUBLIC_USE_APPWRITE=true
NEXT_PUBLIC_APP_URL=https://your-app-domain.appwrite.global
```

## 🔧 Pre-Deployment Checklist

1. **Verify package.json exists in root directory**
2. **Ensure all dependencies are listed in package.json**
3. **Test build locally**: `npm run build`
4. **Verify Next.js configuration is correct**
5. **Set up Appwrite database and storage bucket**

## 📋 Deployment Steps

### 1. Prepare Your Repository
```bash
# Ensure clean build
npm install
npm run build

# Commit all changes
git add .
git commit -m "Prepare for Appwrite deployment"
git push origin main
```

### 2. Configure Appwrite Project
1. Create new project in Appwrite Console
2. Set up database collections (run `npm run appwrite:setup`)
3. Create storage bucket for product images
4. Note down Project ID and API Key

### 3. Deploy to Appwrite
1. Go to Appwrite Console > Your Project > Hosting
2. Connect your Git repository
3. Configure build settings (see above)
4. Set environment variables
5. Deploy

## 🐛 Troubleshooting Build Issues

### Error: "Could not read package.json"
**Solution**: Ensure Root Directory is set to `.` (dot) in build settings

### Error: "npm install failed"
**Solution**: Check that all dependencies in package.json are valid

### Error: "Build command failed"
**Solution**: 
1. Test `npm run build` locally
2. Check for TypeScript errors
3. Verify Next.js configuration

### Error: "Output directory not found"
**Solution**: Ensure Output Directory is set to `.next`

## 🔍 Verification

After successful deployment:
1. Check that the app loads correctly
2. Verify authentication works
3. Test database connections
4. Confirm image uploads work
5. Check all API endpoints

## 📞 Support

If you encounter issues:
1. Check Appwrite Console build logs
2. Verify all environment variables are set
3. Test the build process locally
4. Review this deployment guide

## 🎉 Success!

Once deployed, your PasaBuy Pal app will be available at:
`https://your-project-id.appwrite.global`
