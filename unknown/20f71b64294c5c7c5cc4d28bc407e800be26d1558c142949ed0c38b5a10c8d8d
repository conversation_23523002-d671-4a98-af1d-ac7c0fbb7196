#!/usr/bin/env tsx

import { serverDatabases, DATABASE_ID, COLLECTIONS } from '../src/lib/appwrite-server'

async function testConnection() {
  console.log('🧪 Testing Appwrite connection...')
  
  try {
    // Test 1: List databases
    console.log('\n📋 Test 1: Checking database connection...')
    const database = await serverDatabases.get(DATABASE_ID)
    console.log(`✅ Connected to database: ${database.name} (${database.$id})`)
    
    // Test 2: List collections
    console.log('\n📋 Test 2: Checking collections...')
    const collections = await serverDatabases.listCollections(DATABASE_ID)
    console.log(`✅ Found ${collections.total} collections:`)
    collections.collections.forEach(collection => {
      console.log(`  - ${collection.name} (${collection.$id})`)
    })
    
    // Test 3: Check store codes collection
    console.log('\n📋 Test 3: Checking store codes data...')
    const storeCodes = await serverDatabases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.STORES
    )
    console.log(`✅ Found ${storeCodes.total} store codes:`)
    storeCodes.documents.forEach(store => {
      console.log(`  - ${store.code}: ${store.name || 'No name'}`)
    })
    
    // Test 4: Check customers collection
    console.log('\n📋 Test 4: Checking customers data...')
    const customers = await serverDatabases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.CUSTOMERS
    )
    console.log(`✅ Found ${customers.total} customers`)
    
    // Test 5: Check orders collection
    console.log('\n📋 Test 5: Checking orders data...')
    const orders = await serverDatabases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.ORDERS
    )
    console.log(`✅ Found ${orders.total} orders`)
    
    // Test 6: Check invoices collection
    console.log('\n📋 Test 6: Checking invoices data...')
    const invoices = await serverDatabases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.INVOICES
    )
    console.log(`✅ Found ${invoices.total} invoices`)
    
    console.log('\n🎉 All tests passed! Appwrite connection is working perfectly!')
    console.log('\n📊 Summary:')
    console.log(`  - Database: Connected ✅`)
    console.log(`  - Collections: ${collections.total} created ✅`)
    console.log(`  - Store Codes: ${storeCodes.total} imported ✅`)
    console.log(`  - Customers: ${customers.total} imported ✅`)
    console.log(`  - Orders: ${orders.total} imported ✅`)
    console.log(`  - Invoices: ${invoices.total} imported ✅`)
    
  } catch (error: any) {
    console.error('❌ Connection test failed:', error.message)
    console.error('Full error:', error)
    process.exit(1)
  }
}

// Run the test
testConnection()
