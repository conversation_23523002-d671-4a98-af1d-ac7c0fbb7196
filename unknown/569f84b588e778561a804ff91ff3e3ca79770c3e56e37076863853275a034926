#!/usr/bin/env tsx

import { serverUsers } from '../src/lib/appwrite-server'
import { ID } from 'node-appwrite'

async function testAuthFlow() {
  console.log('🔐 Testing Appwrite authentication flow...')
  
  try {
    // Test 1: List existing users
    console.log('\n📋 Test 1: Checking existing users...')
    const users = await serverUsers.list()
    console.log(`✅ Found ${users.total} existing users`)
    
    if (users.total > 0) {
      console.log('Existing users:')
      users.users.forEach(user => {
        console.log(`  - ${user.name} (${user.email}) - Status: ${user.status ? 'Active' : 'Inactive'}`)
      })
    }
    
    // Test 2: Create a test user (if not exists)
    console.log('\n📋 Test 2: Creating test user...')
    const testEmail = '<EMAIL>'
    const testPassword = 'TestPassword123!'
    const testName = 'Test User'
    
    try {
      const newUser = await serverUsers.create(
        ID.unique(),
        testEmail,
        undefined, // phone
        testPassword,
        testName
      )
      console.log(`✅ Test user created: ${newUser.name} (${newUser.email})`)
    } catch (error: any) {
      if (error.code === 409) {
        console.log(`ℹ️ Test user already exists: ${testEmail}`)
      } else {
        throw error
      }
    }
    
    // Test 3: List users again to confirm
    console.log('\n📋 Test 3: Confirming user creation...')
    const updatedUsers = await serverUsers.list()
    console.log(`✅ Total users now: ${updatedUsers.total}`)
    
    console.log('\n🎉 Authentication system is working!')
    console.log('\n📋 Next Steps:')
    console.log('  1. Visit http://localhost:3001/auth/register to create your account')
    console.log('  2. Or visit http://localhost:3001/auth/login to sign in')
    console.log('  3. Test the full application with authentication')
    
  } catch (error: any) {
    console.error('❌ Authentication test failed:', error.message)
    console.error('Full error:', error)
    process.exit(1)
  }
}

// Run the test
testAuthFlow()
