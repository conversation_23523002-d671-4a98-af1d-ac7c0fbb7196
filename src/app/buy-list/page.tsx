'use client'

import { Card } from '@/components/ui/card'
import { SimplePageWrapper } from '@/components/layout/page-wrapper'
import Link from 'next/link'
import { useStores } from '@/hooks/use-appwrite-data'

export default function BuyListPage() {
  const { data: storeCodes, isLoading, error } = useStores()

  if (isLoading) {
    return (
      <SimplePageWrapper title="Buy List">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </SimplePageWrapper>
    )
  }

  if (error) {
    return (
      <SimplePageWrapper title="Buy List">
        <div className="text-center py-8">
          <p className="text-red-600">Error loading stores: {error}</p>
        </div>
      </SimplePageWrapper>
    )
  }

  if (!storeCodes || storeCodes.length === 0) {
    return (
      <SimplePageWrapper title="Buy List">
        <div className="text-center py-8">
          <p className="text-gray-600">No stores found. Create a store first.</p>
        </div>
      </SimplePageWrapper>
    )
  }

  // Filter stores that have orders to buy
  const storesWithOrders = storeCodes.filter((store: any) => (store.orderCount || 0) > 0)
  const ordersToFetch = storesWithOrders.reduce((acc: number, store: any) => acc + (store.orderCount || 0), 0)

  return (
    <SimplePageWrapper title="Buy List">

      {storesWithOrders.length === 0 ? (
        <Card className="p-6">
          <div className="text-center">
            <h2 className="text-base font-medium">No orders to buy</h2>
            <p className="text-sm text-muted-foreground mt-2">
              {storeCodes.length === 0
                ? "Add some store codes to get started with your buy list."
                : "All orders have been purchased! Add new orders to continue shopping."
              }
            </p>
            <p className="text-xs text-muted-foreground mt-3">Use the + button below to add orders.</p>
          </div>
        </Card>
      ) : (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:gap-6">
          {/* All items card */}
          <Link href="/buy-list/all" className="block">
            <Card className="p-4 h-20 flex items-center hover:shadow-md hover:bg-accent/50 transition-all duration-200 cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
              <div className="flex items-center justify-between w-full">
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-base">All Stores</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <p className="text-2xl font-bold text-primary">{ordersToFetch}</p>
                    <p className="text-sm text-muted-foreground">orders to buy</p>
                  </div>
                </div>
                <div className="text-muted-foreground ml-4" aria-hidden="true">
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Card>
          </Link>

          {/* Store code cards */}
          {storesWithOrders.map((storeCode) => (
            <Link key={storeCode.id} href={`/buy-list/${storeCode.code.toLowerCase()}`} className="block">
              <Card className="p-4 h-20 flex items-center hover:shadow-md hover:bg-accent/50 transition-all duration-200 cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
                <div className="flex items-center justify-between w-full">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-base truncate" title={storeCode.name || storeCode.code}>
                      {storeCode.name || storeCode.code}
                    </h3>
                    <div className="flex items-center gap-2 mt-1">
                      <p className="text-2xl font-bold text-blue-600">{storeCode.orderCount}</p>
                      <p className="text-sm text-muted-foreground">
                        {storeCode.orderCount === 1 ? 'order' : 'orders'}
                      </p>
                    </div>
                  </div>
                  <div className="text-muted-foreground ml-4" aria-hidden="true">
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </SimplePageWrapper>
  )
}
