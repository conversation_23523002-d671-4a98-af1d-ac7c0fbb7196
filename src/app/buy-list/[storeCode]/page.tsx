'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

import Link from 'next/link'
import { LuArrowLeft, LuPackage } from 'react-icons/lu'
import { useBulkOperations, calculateSelectAllState } from '@/hooks/use-bulk-operations'
import { BulkActionsBar } from '@/components/bulk/bulk-actions-bar'
import { BulkConfirmationDialog } from '@/components/bulk/bulk-confirmation-dialog'
import { BuyListItemCard } from '@/components/orders/buy-list-order-card'
import { useScrollToTop } from '@/hooks/use-scroll'

type Order = {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  imageFilename: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  resellerPrice: number // For compatibility
  isBought: boolean
  packingStatus: string
  customer?: {
    id: number
    name: string
  } | null
  createdAt: string
  updatedAt: string
}

type StoreCode = {
  id: number
  code: string
  name: string | null
}

export default function StoreCodeBuyListPage() {
  const params = useParams()
  const router = useRouter()
  const storeCodeParam = params.storeCode as string

  const [storeCode, setStoreCode] = useState<StoreCode | null>(null)
  const [orders, setOrders] = useState<Order[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Automatically scroll to top when page loads
  useScrollToTop()

  // Bulk operations
  const bulkOps = useBulkOperations()
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean
    action: 'bought' | 'packed'
    isLoading: boolean
  }>({
    isOpen: false,
    action: 'bought',
    isLoading: false
  })

  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true)
        setError(null)

        // First, get all store codes to find the matching one
        const storeCodesRes = await fetch('/api/appwrite/store-codes')
        if (!storeCodesRes.ok) {
          throw new Error('Failed to fetch store codes')
        }

        const storeCodes = await storeCodesRes.json()
        const matchingStoreCode = storeCodes.find(
          (sc: StoreCode) => sc.code.toLowerCase() === storeCodeParam.toLowerCase()
        )

        if (!matchingStoreCode) {
          throw new Error('Store code not found')
        }

        setStoreCode(matchingStoreCode)

        // Then fetch orders for this store code
        const ordersRes = await fetch(`/api/appwrite/orders?storeCodeId=${matchingStoreCode.id}&isBought=false`)
        if (!ordersRes.ok) {
          throw new Error('Failed to fetch orders')
        }

        const ordersData = await ordersRes.json()

        // Handle both legacy and advanced response formats
        if (Array.isArray(ordersData)) {
          // Legacy format - direct array
          setOrders(ordersData)
        } else if (ordersData && ordersData.data && Array.isArray(ordersData.data)) {
          // Advanced format - structured response
          setOrders(ordersData.data)
        } else {
          setOrders([])
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setIsLoading(false)
      }
    }

    if (storeCodeParam) {
      fetchData()
    }
  }, [storeCodeParam])

  const handleStatusUpdate = async (orderId: number, field: 'isBought', value: boolean) => {
    try {
      const response = await fetch(`/api/appwrite/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ [field]: value }),
      })

      if (!response.ok) {
        throw new Error('Failed to update order')
      }

      // Remove the order from the list since it's now bought
      setOrders(orders.filter(order => order.id !== orderId))
    } catch (err) {
      console.error('Error updating order:', err)
    }
  }

  // Bulk operations handlers
  const handleBulkMarkAsBought = () => {
    setConfirmDialog({
      isOpen: true,
      action: 'bought',
      isLoading: false
    })
  }

  const handleBulkConfirm = async () => {
    const selectedItemIds = Array.from(bulkOps.selectedItems)
    if (selectedItemIds.length === 0) return

    setConfirmDialog(prev => ({ ...prev, isLoading: true }))

    try {
      const updates = { isBought: true }

      const response = await fetch('/api/appwrite/orders/bulk', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderIds: selectedItemIds,
          updates
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update orders')
      }

      const result = await response.json()

      // Remove updated orders from the list since they're now bought
      setOrders(orders.filter(order => !selectedItemIds.includes(order.id)))

      // Clear selection and close dialog
      bulkOps.clearSelection()
      setConfirmDialog({ isOpen: false, action: 'bought', isLoading: false })

      // Show success message
      console.log(`Successfully updated ${result.updatedCount} orders`)
    } catch (err) {
      console.error('Error in bulk update:', err)
      setConfirmDialog(prev => ({ ...prev, isLoading: false }))
    }
  }

  const handleBulkCancel = () => {
    setConfirmDialog({ isOpen: false, action: 'bought', isLoading: false })
  }

  // Long press handler for entering bulk mode
  const handleOrderLongPress = (orderId: number) => {
    bulkOps.selectItemAndEnterBulkMode(orderId)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(amount)
  }

  const handleCardClick = (orderId: number, event: React.MouseEvent) => {
    // Prevent navigation if clicking on interactive elements like buttons
    const target = event.target as HTMLElement
    const isInteractiveElement = target.closest(
      'button, a, [role="button"], [data-radix-collection-item], [data-state], input, select, textarea'
    )

    if (!isInteractiveElement) {
      router.push(`/orders/${orderId}`)
    }
  }

  const totalOrders = orders.length
  const totalValue = orders.reduce((sum: number, order: Order) => sum + (order.quantity * order.storePrice), 0)
  const totalProfit = orders.reduce((sum: number, order: Order) => sum + (order.quantity * order.pasabuyFee), 0)

  if (isLoading) {
    return (
      <div className="space-y-6 py-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (error || !storeCode) {
    return (
      <div className="space-y-6 py-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/buy-list">
              <LuArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-semibold tracking-tight">Store Not Found</h1>
        </div>

        <Card className="p-6">
          <div className="text-center">
            <h2 className="text-sm font-medium text-red-600">Error</h2>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
            <Button asChild className="mt-4">
              <Link href="/buy-list">Back to Buy List</Link>
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6 py-4">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild>
          <Link href="/buy-list">
            <LuArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl font-semibold tracking-tight">
            {storeCode.name || storeCode.code} Buy List
          </h1>
          <p className="text-muted-foreground">
            {totalOrders} orders to buy at {storeCode.code}
          </p>
        </div>

      </div>

      {/* Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        <Card className="p-3">
          <div className="text-center">
            <p className="text-xl font-bold text-blue-600">{totalOrders}</p>
            <p className="text-xs text-muted-foreground">Orders to Buy</p>
          </div>
        </Card>
        <Card className="p-3">
          <div className="text-center">
            <p className="text-xl font-bold text-green-600">{formatCurrency(totalValue)}</p>
            <p className="text-xs text-muted-foreground">Total Store Cost</p>
          </div>
        </Card>
        <Card className="p-3">
          <div className="text-center">
            <p className="text-xl font-bold text-purple-600">{formatCurrency(totalProfit)}</p>
            <p className="text-xs text-muted-foreground">Total Profit</p>
          </div>
        </Card>
      </div>

      {/* Bulk Actions Bar */}
      {totalOrders > 0 && (bulkOps.isBulkMode || bulkOps.hasSelection) && (
        <BulkActionsBar
          selectedCount={bulkOps.selectedCount}
          totalCount={totalOrders}
          isSelectAllChecked={calculateSelectAllState(bulkOps.selectedItems, orders.map(order => order.id)).isSelectAllChecked}
          isSelectAllIndeterminate={calculateSelectAllState(bulkOps.selectedItems, orders.map(order => order.id)).isSelectAllIndeterminate}
          isBulkMode={bulkOps.isBulkMode}
          onSelectAllChange={() => bulkOps.toggleSelectAll(orders.map(order => order.id))}
          onClearSelection={bulkOps.clearSelection}
          onExitBulkMode={bulkOps.exitBulkMode}
          onMarkAsBought={handleBulkMarkAsBought}
          showBoughtAction={true}
          showPackedAction={false}
        />
      )}

      {/* Orders List */}
      {totalOrders === 0 ? (
        <Card className="p-6">
          <div className="text-center">
            <LuPackage className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="text-lg font-medium mt-2">No orders to buy</h3>
            <p className="text-sm text-muted-foreground mt-1">
              All orders for {storeCode.code} have been purchased, or no orders have been added yet.
            </p>
            <div className="flex gap-2 justify-center mt-4">
              <Button asChild variant="outline">
                <Link href="/buy-list">Back to Buy List</Link>
              </Button>
              <p className="text-xs text-muted-foreground mt-2">Use the + button below to add an order to this store.</p>
            </div>
          </div>
        </Card>
      ) : (
        <div className="grid gap-2">
          {orders.map((order) => (
            <BuyListItemCard
              key={order.id}
              item={order}
              isSelected={bulkOps.isItemSelected(order.id)}
              isBulkMode={bulkOps.isBulkMode}
              onToggleSelection={bulkOps.toggleItem}
              onLongPress={handleOrderLongPress}
              onMarkAsBought={(orderId: number) => handleStatusUpdate(orderId, 'isBought', true)}
              onCardClick={handleCardClick}
            />
          ))}
        </div>
      )}

      {/* Bulk Confirmation Dialog */}
      <BulkConfirmationDialog
        isOpen={confirmDialog.isOpen}
        onClose={handleBulkCancel}
        onConfirm={handleBulkConfirm}
        action={confirmDialog.action}
        selectedCount={bulkOps.selectedCount}
        isLoading={confirmDialog.isLoading}
      />
    </div>
  )
}
