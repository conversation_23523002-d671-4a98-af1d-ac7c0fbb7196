import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/lib/auth-server';
import { appwriteStoreService } from '@/lib/services/appwrite-store-service';
import { type ValidationResult, type EnhancedStoreFormData } from '@/components/forms/store-code-form'; // Assuming EnhancedStoreFormData is also exported or define relevant part

// Define a minimal type for what storeData contains, including a potential id
interface StoreValidationData extends Partial<EnhancedStoreFormData> {
  id?: string | number; // Assuming id could be string or number from Appwrite
}

export async function POST(request: NextRequest) {
  try {
    const userId = await getUserIdFromRequest(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const storeData = body.storeData as StoreValidationData;
    const isUpdate = body.isUpdate as boolean;

    const validationResult: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [], // Keeping warnings array for compatibility, though focusing on errors with severity
    };

    // --- Code Uniqueness Check ---
    if (storeData.code) {
      const codeToCheck = storeData.code.trim();
      if (codeToCheck) {
        // Use getStoreCodeByCode for exact code matching
        const existingStore = await appwriteStoreService.getStoreCodeByCode(codeToCheck, userId);

        if (existingStore) {
          if (isUpdate && existingStore.id === String(storeData.id)) {
            // This is the same store being updated, so its own code is not a conflict.
          } else {
            validationResult.isValid = false;
            validationResult.errors.push({
              field: 'code',
              value: storeData.code,
              errorCode: 'CODE_NOT_UNIQUE',
              errorMessage: `Store code '${storeData.code}' already exists.`,
              severity: 'ERROR',
            });
          }
        }
      } else if (!isUpdate) { // Code is required for new stores, Zod handles on client, but good to have server check
          validationResult.isValid = false;
          validationResult.errors.push({
            field: 'code',
            errorCode: 'CODE_REQUIRED',
            errorMessage: 'Store code is required.',
            severity: 'ERROR',
          });
      }
    } else if (!isUpdate) {
        validationResult.isValid = false;
        validationResult.errors.push({
            field: 'code',
            errorCode: 'CODE_REQUIRED',
            errorMessage: 'Store code is required.',
            severity: 'ERROR',
        });
    }

    // --- Name Uniqueness Check (Optional Example) ---
    // Add similar logic for 'name' if it needs to be unique
    // if (storeData.name) {
    //   const nameToCheck = storeData.name.trim();
    //   if (nameToCheck) {
    //     const existingStoresByName = await appwriteStoreService.searchStoreCodes({ name: nameToCheck }, userId);
    //     for (const existingStore of existingStoresByName.documents) {
    //       if (isUpdate && existingStore.$id === String(storeData.id)) {
    //         // Same store
    //       } else {
    //         validationResult.isValid = false; // Or perhaps a warning for name
    //         validationResult.errors.push({
    //           field: 'name',
    //           value: storeData.name,
    //           errorCode: 'NAME_NOT_UNIQUE',
    //           errorMessage: `Store name '${storeData.name}' already exists.`,
    //           severity: 'ERROR', // or 'WARNING'
    //         });
    //         break;
    //       }
    //     }
    //   }
    // }

    // Add other validation rules as needed

    // If errors were found, ensure isValid is false
    if (validationResult.errors.some(e => e.severity === 'ERROR')) {
        validationResult.isValid = false;
    }


    return NextResponse.json(validationResult);

  } catch (error: any) {
    console.error('Validation API error:', error);
    return NextResponse.json(
      {
        isValid: false,
        errors: [{ field: 'general', errorCode: 'VALIDATION_SERVER_ERROR', errorMessage: 'Server error during validation.', severity: 'ERROR' }],
        warnings: [],
      },
      { status: 500 }
    );
  }
}
