import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/lib/auth-server';
import { appwriteStorePricingService, StorePricingRule } from '@/lib/services/appwrite-store-pricing-service';

// RouteContext interface removed

export async function GET(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  let ruleId: string | undefined;
  try {
    const userId = await getUserIdFromRequest(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;
    ruleId = id;
    if (!ruleId) {
      return NextResponse.json({ error: 'Rule ID is required' }, { status: 400 });
    }

    const rule = await appwriteStorePricingService.getStorePricingRuleById(ruleId, userId);
    if (!rule) {
      return NextResponse.json({ error: 'Store pricing rule not found or access denied' }, { status: 404 });
    }
    return NextResponse.json(rule);
  } catch (error: any) {
    console.error(`Error fetching store pricing rule ${ruleId || 'unknown'}:`, error);
    return NextResponse.json({ error: error.message || 'Failed to fetch store pricing rule' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  let ruleId: string | undefined;
  try {
    const userId = await getUserIdFromRequest(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;
    ruleId = id;
    if (!ruleId) {
      return NextResponse.json({ error: 'Rule ID is required' }, { status: 400 });
    }

    const body = await request.json();
    // Basic validation: ensure not sending empty body or critical unchangeable fields if necessary
    if (Object.keys(body).length === 0) {
        return NextResponse.json({ error: 'Request body cannot be empty for update.' }, { status: 400 });
    }
    // Prevent changing userId or id via PUT body, though service should handle this too
    const { userId: bodyUserId, id: bodyId, storeCodeId: bodyStoreCodeId, ...updateData } = body;


    const updatedRule = await appwriteStorePricingService.updateStorePricingRule(ruleId, updateData as Partial<Omit<StorePricingRule, '$id' | 'userId' | '$createdAt' | '$updatedAt'>>, userId);
    if (!updatedRule) { // Should not happen with mock, but good for real service
        return NextResponse.json({ error: 'Failed to update rule or rule not found' }, { status: 404 });
    }
    return NextResponse.json(updatedRule);
  } catch (error: any) {
    console.error(`Error updating store pricing rule ${ruleId || 'unknown'}:`, error);
    return NextResponse.json({ error: error.message || 'Failed to update store pricing rule' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  let ruleId: string | undefined;
  try {
    const userId = await getUserIdFromRequest(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;
    ruleId = id;
    if (!ruleId) {
      return NextResponse.json({ error: 'Rule ID is required' }, { status: 400 });
    }

    await appwriteStorePricingService.deleteStorePricingRule(ruleId, userId);
    return NextResponse.json({ message: 'Store pricing rule deleted successfully' }, { status: 200 }); // Or 204 No Content
  } catch (error: any) {
    console.error(`Error deleting store pricing rule ${ruleId || 'unknown'}:`, error);
    if (error.message && error.message.toLowerCase().includes('not found')) {
        return NextResponse.json({ error: error.message }, { status: 404 });
    }
    return NextResponse.json({ error: error.message || 'Failed to delete store pricing rule' }, { status: 500 });
  }
}
