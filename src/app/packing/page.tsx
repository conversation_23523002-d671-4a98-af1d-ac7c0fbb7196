'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar } from '@/components/ui/avatar'
import { SimplePageWrapper } from '@/components/layout/page-wrapper'
import Link from 'next/link'
import { LuPackage } from 'react-icons/lu'
import { useAppStore } from '@/lib/store'



export default function PackingPage() {
  const { customers, setCustomers, isLoadingCustomers, setLoadingCustomers } = useAppStore()
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchCustomers() {
      try {
        setLoadingCustomers(true)
        setError(null)

        const response = await fetch('/api/appwrite/customers')
        if (!response.ok) {
          throw new Error('Failed to fetch customers')
        }

        const data = await response.json()
        setCustomers(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoadingCustomers(false)
      }
    }

    fetchCustomers()
  }, [setCustomers, setLoadingCustomers])

  // Filter customers that have orders to pack
  const customersWithOrdersToPack = customers.filter(customer => customer._count?.toPack && customer._count.toPack > 0)

  if (error) {
    return (
      <SimplePageWrapper title="Packing">
        <Card className="p-3">
          <div className="text-center">
            <h2 className="text-sm font-medium text-destructive">Error</h2>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              className="mt-4 h-7"
            >
              Try Again
            </Button>
          </div>
        </Card>
      </SimplePageWrapper>
    )
  }

  return (
    <SimplePageWrapper title="Packing">
      <div className="text-sm text-muted-foreground mb-4">
        Orders that are bought but not yet packed, organized by customer.
      </div>

      {isLoadingCustomers ? (
        <div className="grid gap-2 sm:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="p-3">
              <div className="animate-pulse">
                <div className="flex items-center space-x-2">
                  <div className="w-7 h-7 bg-muted/50 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-muted/50 rounded w-3/4"></div>
                    <div className="h-3 bg-muted/50 rounded w-1/2 mt-2"></div>
                  </div>
                </div>
                <div className="mt-3 h-7 bg-muted/50 rounded"></div>
              </div>
            </Card>
          ))}
        </div>
      ) : customersWithOrdersToPack.length === 0 ? (
        <Card className="p-3">
          <div className="text-center">
            <LuPackage className="mx-auto h-12 w-12 text-muted-foreground" />
            <h2 className="text-sm font-medium mt-2">No orders to pack</h2>
            <p className="text-sm text-muted-foreground mt-1">
              All orders are either not bought yet or already packed.
            </p>
            <div className="flex gap-2 justify-center mt-4">
              <Button asChild variant="outline" className="h-7">
                <Link href="/buy-list">View Buy List</Link>
              </Button>
              <p className="text-xs text-muted-foreground mt-2">Use the + button below to add an order.</p>
            </div>
          </div>
        </Card>
      ) : (
        <div className="grid gap-2 sm:grid-cols-2 lg:grid-cols-3">
          {customersWithOrdersToPack.map((customer) => (
            <Card key={customer.id} className="p-3 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 flex-1 min-w-0">
                  <Avatar className="h-7 w-7">
                    <div className="w-full h-full bg-primary flex items-center justify-center text-primary-foreground font-medium text-xs">
                      {customer.name.charAt(0).toUpperCase()}
                    </div>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-sm truncate">{customer.name}</h3>
                    <div className="flex items-center gap-1.5 mt-0.5">
                      <div className="flex gap-1">
                        <Badge variant="default" className="text-xs px-1 py-0 h-4">
                          {customer._count?.toPack || 0} to pack
                        </Badge>
                        {(customer._count?.toBuy || 0) > 0 && (
                          <Badge variant="secondary" className="text-xs px-1 py-0 h-4">
                            {customer._count?.toBuy || 0} to buy
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex-shrink-0 ml-2">
                  <Button asChild variant="outline" size="sm" className="h-7 px-2 text-xs">
                    <Link href={`/packing/${customer.id}`}>
                      Pack Orders
                    </Link>
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </SimplePageWrapper>
  )
}
