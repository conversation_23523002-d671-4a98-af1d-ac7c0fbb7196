'use client'

import { useEffect, useState, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Link from 'next/link'
import { LuArrowLeft, LuDownload, LuPencil, LuTrash2, LuCalendar, LuUser, LuFileText } from 'react-icons/lu'
import { Invoice, InvoiceStatus } from '@/lib/store'
import { generateInvoicePDF } from '@/lib/pdf-generator'
import { useScrollToTop } from '@/hooks/use-scroll'

const statusColors: Record<InvoiceStatus, string> = {
  DRAFT: 'bg-muted/60 text-muted-foreground border-border dark:bg-muted/40 dark:text-muted-foreground',
  SENT: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800/50',
  PAID: 'bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-800/50',
  OVERDUE: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800/50',
  CANCELLED: 'bg-muted/60 text-muted-foreground border-border dark:bg-muted/40 dark:text-muted-foreground',
}

const statusLabels: Record<InvoiceStatus, string> = {
  DRAFT: 'Draft',
  SENT: 'Sent',
  PAID: 'Paid',
  OVERDUE: 'Overdue',
  CANCELLED: 'Cancelled',
}

export default function InvoiceDetailPage() {
  const [invoice, setInvoice] = useState<Invoice | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isUpdating, setIsUpdating] = useState(false)
  const [invoiceId, setInvoiceId] = useState<string>('')
  const params = useParams()
  const router = useRouter()

  // Automatically scroll to top when page loads
  useScrollToTop()

  useEffect(() => {
    // Handle async params
    const resolveParams = async () => {
      const resolvedParams = await params
      setInvoiceId(resolvedParams.id as string)
    }
    resolveParams()
  }, [params])

  const fetchInvoice = useCallback(async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/appwrite/invoices/${invoiceId}`)
      if (response.ok) {
        const data = await response.json()
        setInvoice(data)
      } else {
        router.push('/invoices')
      }
    } catch (error) {
      console.error('Error fetching invoice:', error)
      router.push('/invoices')
    } finally {
      setIsLoading(false)
    }
  }, [invoiceId, router])

  useEffect(() => {
    if (invoiceId) {
      fetchInvoice()
    }
  }, [invoiceId, fetchInvoice])

  const handleStatusUpdate = async (newStatus: InvoiceStatus) => {
    if (!invoice) return

    try {
      setIsUpdating(true)
      const response = await fetch(`/api/appwrite/invoices/${invoice.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        const updatedInvoice = await response.json()
        setInvoice(updatedInvoice)
      }
    } catch (error) {
      console.error('Error updating invoice status:', error)
    } finally {
      setIsUpdating(false)
    }
  }

  const handleExportPDF = () => {
    if (!invoice) return

    try {
      const pdf = generateInvoicePDF(invoice)
      pdf.save(`${invoice.invoiceNumber}.pdf`)
    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Failed to generate PDF')
    }
  }

  const handleDelete = async () => {
    if (!invoice) return

    if (confirm('Are you sure you want to delete this invoice? This action cannot be undone.')) {
      try {
        const response = await fetch(`/api/appwrite/invoices/${invoice.id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          router.push('/invoices')
        }
      } catch (error) {
        console.error('Error deleting invoice:', error)
      }
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-PH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  if (isLoading) {
    return (
      <div className="space-y-6 py-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (!invoice) {
    return (
      <div className="space-y-6 py-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Invoice not found</h1>
          <Link href="/invoices">
            <Button className="mt-4">Back to Invoices</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 py-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Link href="/invoices">
            <Button variant="outline" size="icon" className="min-w-[44px] min-h-[44px]">
              <LuArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <div className="flex items-center gap-3 mb-1">
              <h1 className="text-2xl font-bold">{invoice.invoiceNumber}</h1>
              <Badge className={`${statusColors[invoice.status]} text-sm font-medium`}>
                {statusLabels[invoice.status]}
              </Badge>
            </div>
            <p className="text-muted-foreground">
              Invoice details and line items
            </p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          <Link href={`/invoices/${invoice.id}/edit`}>
            <Button variant="outline" className="min-h-[44px] w-full sm:w-auto">
              <LuPencil className="h-5 w-5 mr-2" />
              Edit Invoice
            </Button>
          </Link>
          <Button variant="outline" className="min-h-[44px]" onClick={handleExportPDF}>
            <LuDownload className="h-5 w-5 mr-2" />
            Export PDF
          </Button>
          <Button variant="outline" className="min-h-[44px]" onClick={handleDelete}>
            <LuTrash2 className="h-5 w-5 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Invoice Info */}
      <Card className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <LuUser className="h-4 w-4" />
              <span>Customer</span>
            </div>
            <p className="font-medium">{invoice.customer?.name}</p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <LuCalendar className="h-4 w-4" />
              <span>Created</span>
            </div>
            <p className="font-medium">{formatDate(invoice.createdAt)}</p>
          </div>

          {invoice.dueDate && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <LuCalendar className="h-4 w-4" />
                <span>Due Date</span>
              </div>
              <p className="font-medium">{formatDate(invoice.dueDate)}</p>
            </div>
          )}

          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <LuFileText className="h-4 w-4" />
              <span>Status</span>
            </div>
            <Select
              value={invoice.status}
              onValueChange={(value) => handleStatusUpdate(value as InvoiceStatus)}
              disabled={isUpdating}
            >
              <SelectTrigger className="min-h-[44px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(statusLabels).map(([status, label]) => (
                  <SelectItem key={status} value={status}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {invoice.notes && (
          <div className="mt-6 pt-6 border-t">
            <h4 className="font-medium mb-2">Notes</h4>
            <p className="text-muted-foreground">{invoice.notes}</p>
          </div>
        )}
      </Card>

      {/* Invoice Orders */}
      <Card className="p-6">
        <h3 className="text-lg font-medium mb-4">Invoice Orders</h3>
        <div className="space-y-3">
          {invoice.invoiceItems?.map((invoiceOrder) => (
            <div key={invoiceOrder.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex-1 min-w-0">
                <h4 className="font-medium truncate">{invoiceOrder.order?.productName}</h4>
                <p className="text-sm text-muted-foreground">
                  {formatCurrency(invoiceOrder.unitPrice)} × {invoiceOrder.quantity}
                </p>
                {invoiceOrder.order?.storeCode && (
                  <p className="text-xs text-muted-foreground">
                    Store: {invoiceOrder.order.storeCode.name || invoiceOrder.order.storeCode.code}
                  </p>
                )}
              </div>
              <div className="text-right">
                <p className="font-medium">{formatCurrency(invoiceOrder.totalPrice)}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Totals */}
        <div className="mt-6 pt-6 border-t">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>{formatCurrency(invoice.subtotal)}</span>
            </div>
            <div className="flex justify-between text-lg font-medium">
              <span>Total:</span>
              <span>{formatCurrency(invoice.total)}</span>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}
