'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'

interface BaseFormProps<T extends Record<string, unknown>> {
  schema: z.ZodSchema<T>
  onSubmit: (data: T) => Promise<void>
  onCancel: () => void
  initialData?: Partial<T>
  isLoading?: boolean
  children: (form: ReturnType<typeof useForm<T>>) => React.ReactNode
  submitButtonText?: string
  cancelButtonText?: string
}

export function BaseForm<T extends Record<string, unknown>>({
  schema,
  onSubmit,
  onCancel,
  initialData,
  isLoading = false,
  children,
  submitButtonText = 'Save',
  cancelButtonText = 'Cancel'
}: BaseFormProps<T>) {
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues: {
      ...initialData,
    } as any,
  })

  const handleSubmit = async (data: T) => {
    await onSubmit(data)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {children(form)}

        {/* Form Actions - Mobile-optimized with larger touch targets */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="flex-1 min-h-[48px] text-base font-medium order-2 sm:order-1"
          >
            {cancelButtonText}
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="flex-1 min-h-[48px] text-base font-medium order-1 sm:order-2"
          >
            {isLoading ? 'Saving...' : submitButtonText}
          </Button>
        </div>
      </form>
    </Form>
  )
}
