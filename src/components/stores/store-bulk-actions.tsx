'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { LuTrash2, LuPencil, LuX, LuLoader, LuTriangleAlert, LuCircleCheck } from 'react-icons/lu'

interface BulkOperationResult {
  operationId: string
  operationType: 'CREATE' | 'UPDATE' | 'DELETE'
  totalItems: number
  processedItems: number
  successfulItems: number
  failedItems: number
  errors: Array<{
    itemIndex: number
    itemData?: any
    errorCode: string
    errorMessage: string
    field?: string
  }>
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  startedAt: Date
  completedAt?: Date
}

interface StoreBulkActionsBarProps {
  selectedCount: number
  totalCount: number
  isSelectAllChecked: boolean
  isSelectAllIndeterminate: boolean
  isBulkMode?: boolean
  onSelectAllChange: () => void
  onClearSelection: () => void
  onExitBulkMode?: () => void
  onBulkUpdate?: () => void
  onBulkDelete?: () => void
  className?: string
}

interface StoreBulkConfirmationDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  action: 'update' | 'delete'
  selectedCount: number
  isLoading?: boolean
  operationResult?: BulkOperationResult | null
}

export function StoreBulkActionsBar({
  selectedCount,
  totalCount,
  isSelectAllChecked,
  isSelectAllIndeterminate,
  isBulkMode = false,
  onSelectAllChange,
  onClearSelection,
  onExitBulkMode,
  onBulkUpdate,
  onBulkDelete,
  className = ''
}: StoreBulkActionsBarProps) {
  if (selectedCount === 0) {
    return (
      <Card className={`p-3 ${className}`}>
        <div className="flex items-center gap-3">
          <Checkbox
            checked={isSelectAllChecked}
            ref={(el) => {
              if (el) {
                (el as HTMLInputElement).indeterminate = isSelectAllIndeterminate
              }
            }}
            onCheckedChange={onSelectAllChange}
            aria-label="Select all stores"
          />
          <span className="text-sm text-muted-foreground">
            Select stores for bulk actions ({totalCount} total)
          </span>
        </div>
      </Card>
    )
  }

  return (
    <Card className={`p-3 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Checkbox
            checked={isSelectAllChecked}
            ref={(el) => {
              if (el) {
                (el as HTMLInputElement).indeterminate = isSelectAllIndeterminate
              }
            }}
            onCheckedChange={onSelectAllChange}
            aria-label="Select all stores"
          />
          <span className="text-sm font-medium">
            {selectedCount} of {totalCount} stores selected
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearSelection}
            className="h-6 px-2 text-xs"
          >
            Clear
          </Button>
        </div>

        <div className="flex items-center gap-2">
          {onBulkUpdate && (
            <Button
              variant="outline"
              size="sm"
              onClick={onBulkUpdate}
              className="h-8 px-3 text-xs"
            >
              <LuPencil className="h-3 w-3 mr-1" />
              Bulk Update
            </Button>
          )}
          
          {onBulkDelete && (
            <Button
              variant="outline"
              size="sm"
              onClick={onBulkDelete}
              className="h-8 px-3 text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <LuTrash2 className="h-3 w-3 mr-1" />
              Delete Selected
            </Button>
          )}

          {onExitBulkMode && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onExitBulkMode}
              className="h-8 px-2"
            >
              <LuX className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
    </Card>
  )
}

export function StoreBulkConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  action,
  selectedCount,
  isLoading = false,
  operationResult = null
}: StoreBulkConfirmationDialogProps) {
  const getActionText = () => {
    switch (action) {
      case 'update':
        return {
          title: 'Bulk Update Stores',
          description: `Are you sure you want to update ${selectedCount} selected store(s)?`,
          confirmText: 'Update Stores',
          icon: LuPencil,
          color: 'blue'
        }
      case 'delete':
        return {
          title: 'Delete Stores',
          description: `Are you sure you want to delete ${selectedCount} selected store(s)? This action cannot be undone.`,
          confirmText: 'Delete Stores',
          icon: LuTrash2,
          color: 'red'
        }
      default:
        return {
          title: 'Confirm Action',
          description: `Are you sure you want to perform this action on ${selectedCount} store(s)?`,
          confirmText: 'Confirm',
          icon: LuCircleCheck,
          color: 'blue'
        }
    }
  }

  const actionConfig = getActionText()
  const IconComponent = actionConfig.icon

  if (operationResult) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {operationResult.status === 'COMPLETED' ? (
                <LuCircleCheck className="h-5 w-5 text-green-600" />
              ) : operationResult.status === 'FAILED' ? (
                <LuTriangleAlert className="h-5 w-5 text-red-600" />
              ) : (
                <LuLoader className="h-5 w-5 animate-spin" />
              )}
              Operation {operationResult.status}
            </DialogTitle>
            <DialogDescription>
              Bulk {operationResult.operationType.toLowerCase()} operation results
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Progress Summary */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{operationResult.totalItems}</div>
                <div className="text-sm text-muted-foreground">Total</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{operationResult.successfulItems}</div>
                <div className="text-sm text-muted-foreground">Successful</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{operationResult.failedItems}</div>
                <div className="text-sm text-muted-foreground">Failed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{operationResult.processedItems}</div>
                <div className="text-sm text-muted-foreground">Processed</div>
              </div>
            </div>

            {/* Errors */}
            {operationResult.errors.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-red-700">Errors:</h4>
                <div className="max-h-48 overflow-y-auto space-y-2">
                  {operationResult.errors.map((error, index) => (
                    <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="text-sm text-red-800">
                        <strong>Item {error.itemIndex + 1}:</strong> {error.errorMessage}
                      </div>
                      {error.field && (
                        <div className="text-xs text-red-600 mt-1">
                          Field: {error.field}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Success Message */}
            {operationResult.status === 'COMPLETED' && operationResult.failedItems === 0 && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2 text-green-800">
                  <LuCircleCheck className="h-4 w-4" />
                  <span className="font-medium">
                    All {operationResult.successfulItems} stores were successfully {operationResult.operationType.toLowerCase()}d!
                  </span>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button onClick={onClose}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <IconComponent className={`h-5 w-5 ${action === 'delete' ? 'text-red-600' : 'text-blue-600'}`} />
            {actionConfig.title}
          </DialogTitle>
          <DialogDescription>
            {actionConfig.description}
          </DialogDescription>
        </DialogHeader>

        {action === 'delete' && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-2">
              <LuTriangleAlert className="h-4 w-4 text-red-600 mt-0.5" />
              <div className="text-sm text-red-800">
                <div className="font-medium">Warning: This action is irreversible</div>
                <div className="mt-1">
                  Deleting stores will also remove all associated data including orders, 
                  configurations, and metrics. Make sure you have backups if needed.
                </div>
              </div>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            disabled={isLoading}
            variant={action === 'delete' ? 'destructive' : 'default'}
            className="min-w-[120px]"
          >
            {isLoading ? (
              <>
                <LuLoader className="h-4 w-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <IconComponent className="h-4 w-4 mr-2" />
                {actionConfig.confirmText}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
