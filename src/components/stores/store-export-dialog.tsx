'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { LuDownload, LuLoader, LuFilter } from 'react-icons/lu'

interface ExportFilters {
  storeType?: string[]
  status?: string[]
  region?: string
  city?: string
  isOpen?: boolean
  allowsDelivery?: boolean
  allowsPickup?: boolean
}

interface StoreExportDialogProps {
  isOpen: boolean
  onClose: () => void
  onExportComplete?: () => void
}

export function StoreExportDialog({ isOpen, onClose, onExportComplete }: StoreExportDialogProps) {
  const [format, setFormat] = useState<'CSV' | 'EXCEL'>('EXCEL')
  const [includeMetrics, setIncludeMetrics] = useState(true)
  const [includeConfigurations, setIncludeConfigurations] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Filter states
  const [selectedStoreTypes, setSelectedStoreTypes] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [regionFilter, setRegionFilter] = useState('')
  const [cityFilter, setCityFilter] = useState('')
  const [isOpenFilter, setIsOpenFilter] = useState<boolean | undefined>(undefined)
  const [allowsDeliveryFilter, setAllowsDeliveryFilter] = useState<boolean | undefined>(undefined)
  const [allowsPickupFilter, setAllowsPickupFilter] = useState<boolean | undefined>(undefined)

  // Custom field selection
  const [selectedFields, setSelectedFields] = useState<string[]>([
    'code', 'name', 'storeType', 'status', 'city', 'phone', 'email'
  ])

  const availableFields = [
    { id: 'code', label: 'Store Code', required: true },
    { id: 'name', label: 'Store Name' },
    { id: 'storeType', label: 'Store Type' },
    { id: 'status', label: 'Status' },
    { id: 'address', label: 'Address' },
    { id: 'city', label: 'City' },
    { id: 'state', label: 'State/Province' },
    { id: 'postalCode', label: 'Postal Code' },
    { id: 'country', label: 'Country' },
    { id: 'phone', label: 'Phone' },
    { id: 'email', label: 'Email' },
    { id: 'website', label: 'Website' },
    { id: 'managerName', label: 'Manager Name' },
    { id: 'managerPhone', label: 'Manager Phone' },
    { id: 'contactPerson', label: 'Contact Person' },
    { id: 'operatingHours', label: 'Operating Hours' },
    { id: 'timezone', label: 'Timezone' },
    { id: 'isOpen', label: 'Is Open' },
    { id: 'allowsPickup', label: 'Allows Pickup' },
    { id: 'allowsDelivery', label: 'Allows Delivery' },
    { id: 'deliveryRadius', label: 'Delivery Radius' },
    { id: 'minimumOrder', label: 'Minimum Order' },
    { id: 'serviceFee', label: 'Service Fee' },
    { id: 'capacity', label: 'Capacity' },
    { id: 'priority', label: 'Priority' },
    { id: 'notes', label: 'Notes' }
  ]

  const storeTypes = [
    'RETAIL', 'WHOLESALE', 'ONLINE', 'MARKETPLACE', 
    'WAREHOUSE', 'DISTRIBUTION_CENTER', 'FRANCHISE', 'CORPORATE'
  ]

  const statuses = [
    'ACTIVE', 'INACTIVE', 'MAINTENANCE', 'TEMPORARILY_CLOSED', 
    'PERMANENTLY_CLOSED', 'UNDER_CONSTRUCTION'
  ]

  const handleStoreTypeChange = (storeType: string, checked: boolean) => {
    if (checked) {
      setSelectedStoreTypes([...selectedStoreTypes, storeType])
    } else {
      setSelectedStoreTypes(selectedStoreTypes.filter(t => t !== storeType))
    }
  }

  const handleStatusChange = (status: string, checked: boolean) => {
    if (checked) {
      setSelectedStatuses([...selectedStatuses, status])
    } else {
      setSelectedStatuses(selectedStatuses.filter(s => s !== status))
    }
  }

  const handleFieldChange = (fieldId: string, checked: boolean) => {
    if (checked) {
      setSelectedFields([...selectedFields, fieldId])
    } else {
      setSelectedFields(selectedFields.filter(f => f !== fieldId))
    }
  }

  const buildExportUrl = () => {
    const params = new URLSearchParams()
    
    // Format
    params.append('format', format)
    
    // Options
    if (includeMetrics) params.append('includeMetrics', 'true')
    if (includeConfigurations) params.append('includeConfigurations', 'true')
    
    // Fields
    if (selectedFields.length > 0) {
      params.append('fields', selectedFields.join(','))
    }
    
    // Filters
    if (selectedStoreTypes.length > 0) {
      params.append('storeType', selectedStoreTypes.join(','))
    }
    if (selectedStatuses.length > 0) {
      params.append('status', selectedStatuses.join(','))
    }
    if (regionFilter) params.append('region', regionFilter)
    if (cityFilter) params.append('city', cityFilter)
    if (isOpenFilter !== undefined) params.append('isOpen', isOpenFilter.toString())
    if (allowsDeliveryFilter !== undefined) params.append('allowsDelivery', allowsDeliveryFilter.toString())
    if (allowsPickupFilter !== undefined) params.append('allowsPickup', allowsPickupFilter.toString())
    
    return `/api/enhanced/stores/export?${params.toString()}`
  }

  const handleExport = async () => {
    setIsExporting(true)
    setError(null)

    try {
      const url = buildExportUrl()
      const response = await fetch(url)

      if (response.ok) {
        const blob = await response.blob()
        const downloadUrl = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = downloadUrl
        
        const timestamp = new Date().toISOString().split('T')[0]
        const extension = format === 'CSV' ? 'csv' : 'xlsx'
        a.download = `stores_export_${timestamp}.${extension}`
        
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(downloadUrl)
        document.body.removeChild(a)
        
        onExportComplete?.()
        onClose()
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Export failed')
      }
    } catch (error) {
      console.error('Export error:', error)
      setError('Failed to export stores')
    } finally {
      setIsExporting(false)
    }
  }

  const resetFilters = () => {
    setSelectedStoreTypes([])
    setSelectedStatuses([])
    setRegionFilter('')
    setCityFilter('')
    setIsOpenFilter(undefined)
    setAllowsDeliveryFilter(undefined)
    setAllowsPickupFilter(undefined)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Export Stores</DialogTitle>
          <DialogDescription>
            Configure export options and download store data
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Format */}
          <Card className="p-4">
            <h4 className="text-sm font-medium mb-3">Export Format</h4>
            <div className="flex gap-4">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="csv"
                  name="format"
                  checked={format === 'CSV'}
                  onChange={() => setFormat('CSV')}
                />
                <Label htmlFor="csv">CSV</Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="excel"
                  name="format"
                  checked={format === 'EXCEL'}
                  onChange={() => setFormat('EXCEL')}
                />
                <Label htmlFor="excel">Excel</Label>
              </div>
            </div>
          </Card>

          {/* Export Options */}
          <Card className="p-4">
            <h4 className="text-sm font-medium mb-3">Export Options</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeMetrics"
                  checked={includeMetrics}
                  onCheckedChange={(checked) => setIncludeMetrics(checked === true)}
                />
                <Label htmlFor="includeMetrics">Include performance metrics</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeConfigurations"
                  checked={includeConfigurations}
                  onCheckedChange={(checked) => setIncludeConfigurations(checked === true)}
                />
                <Label htmlFor="includeConfigurations">Include store configurations</Label>
              </div>
            </div>
          </Card>

          {/* Field Selection */}
          <Card className="p-4">
            <h4 className="text-sm font-medium mb-3">Fields to Export</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-48 overflow-y-auto">
              {availableFields.map((field) => (
                <div key={field.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={field.id}
                    checked={selectedFields.includes(field.id)}
                    onCheckedChange={(checked) => handleFieldChange(field.id, checked as boolean)}
                    disabled={field.required}
                  />
                  <Label htmlFor={field.id} className="text-sm">
                    {field.label}
                    {field.required && <span className="text-red-500 ml-1">*</span>}
                  </Label>
                </div>
              ))}
            </div>
          </Card>

          {/* Filters */}
          <Card className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <LuFilter className="h-4 w-4" />
              <h4 className="text-sm font-medium">Filters</h4>
              <Button variant="ghost" size="sm" onClick={resetFilters}>
                Clear All
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Store Types */}
              <div>
                <Label className="text-sm font-medium">Store Types</Label>
                <div className="mt-2 space-y-2 max-h-32 overflow-y-auto">
                  {storeTypes.map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={`type-${type}`}
                        checked={selectedStoreTypes.includes(type)}
                        onCheckedChange={(checked) => handleStoreTypeChange(type, checked as boolean)}
                      />
                      <Label htmlFor={`type-${type}`} className="text-sm">
                        {type.replace('_', ' ')}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Statuses */}
              <div>
                <Label className="text-sm font-medium">Statuses</Label>
                <div className="mt-2 space-y-2 max-h-32 overflow-y-auto">
                  {statuses.map((status) => (
                    <div key={status} className="flex items-center space-x-2">
                      <Checkbox
                        id={`status-${status}`}
                        checked={selectedStatuses.includes(status)}
                        onCheckedChange={(checked) => handleStatusChange(status, checked as boolean)}
                      />
                      <Label htmlFor={`status-${status}`} className="text-sm">
                        {status.replace('_', ' ')}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Location Filters */}
              <div>
                <Label htmlFor="region" className="text-sm font-medium">Region</Label>
                <Input
                  id="region"
                  placeholder="Filter by region..."
                  value={regionFilter}
                  onChange={(e) => setRegionFilter(e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="city" className="text-sm font-medium">City</Label>
                <Input
                  id="city"
                  placeholder="Filter by city..."
                  value={cityFilter}
                  onChange={(e) => setCityFilter(e.target.value)}
                  className="mt-1"
                />
              </div>

              {/* Operational Filters */}
              <div>
                <Label className="text-sm font-medium">Is Open</Label>
                <Select value={isOpenFilter?.toString() || 'all'} onValueChange={(value) => 
                  setIsOpenFilter(value === 'all' ? undefined : value === 'true')
                }>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="true">Open</SelectItem>
                    <SelectItem value="false">Closed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-sm font-medium">Allows Delivery</Label>
                <Select value={allowsDeliveryFilter?.toString() || 'all'} onValueChange={(value) => 
                  setAllowsDeliveryFilter(value === 'all' ? undefined : value === 'true')
                }>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="true">Yes</SelectItem>
                    <SelectItem value="false">No</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </Card>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="text-red-700 text-sm">{error}</div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting || selectedFields.length === 0}
            className="min-w-[120px]"
          >
            {isExporting ? (
              <>
                <LuLoader className="h-4 w-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <LuDownload className="h-4 w-4 mr-2" />
                Export Stores
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
