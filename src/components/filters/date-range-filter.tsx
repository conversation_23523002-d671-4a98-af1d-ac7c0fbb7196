import React, { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { DateFilter as DateFilterType, DateOperator } from '@/lib/filter-types'
import { Calendar } from 'lucide-react'
import { BaseFilter } from './base-filter'

interface DateRangeFilterProps {
  label: string
  value?: DateFilterType
  onChange: (value: DateFilterType | undefined) => void
}

const operatorLabels: Record<DateOperator, string> = {
  equals: 'On',
  gt: 'After',
  gte: 'On or after',
  lt: 'Before',
  lte: 'On or before',
  between: 'Between'
}

const datePresets = [
  { label: 'Today', getValue: () => new Date() },
  { label: 'Yesterday', getValue: () => {
    const date = new Date()
    date.setDate(date.getDate() - 1)
    return date
  }},
  { label: 'This week', getValue: () => {
    const date = new Date()
    const day = date.getDay()
    const diff = date.getDate() - day
    return new Date(date.setDate(diff))
  }},
  { label: 'Last 7 days', getValue: () => {
    const date = new Date()
    date.setDate(date.getDate() - 7)
    return date
  }},
  { label: 'Last 30 days', getValue: () => {
    const date = new Date()
    date.setDate(date.getDate() - 30)
    return date
  }},
  { label: 'This month', getValue: () => {
    const date = new Date()
    return new Date(date.getFullYear(), date.getMonth(), 1)
  }},
  { label: 'Last month', getValue: () => {
    const date = new Date()
    return new Date(date.getFullYear(), date.getMonth() - 1, 1)
  }}
]

export function DateRangeFilter({ label, value, onChange }: DateRangeFilterProps) {
  const [operator, setOperator] = useState<DateOperator>(value?.operator || 'gte')

  const formatDateForInput = (date: string | Date | undefined) => {
    if (!date) return ''
    const d = typeof date === 'string' ? new Date(date) : date
    return d.toISOString().split('T')[0]
  }

  const handleOperatorChange = (newOperator: DateOperator) => {
    setOperator(newOperator)

    if (newOperator === 'between') {
      onChange({
        operator: newOperator,
        startDate: value?.startDate || new Date(),
        endDate: value?.endDate || new Date()
      })
    } else {
      onChange({
        operator: newOperator,
        value: value?.value || new Date()
      })
    }
  }

  const handleValueChange = (newValue: string) => {
    if (!newValue) {
      onChange(undefined)
      return
    }

    const date = new Date(newValue)
    if (isNaN(date.getTime())) return

    if (operator === 'between') {
      onChange({
        operator,
        startDate: value?.startDate || date,
        endDate: value?.endDate || date
      })
    } else {
      onChange({
        operator,
        value: date
      })
    }
  }

  const handleStartDateChange = (newDate: string) => {
    if (!newDate) return
    const date = new Date(newDate)
    if (isNaN(date.getTime())) return

    onChange({
      operator: 'between',
      startDate: date,
      endDate: value?.endDate || new Date()
    })
  }

  const handleEndDateChange = (newDate: string) => {
    if (!newDate) return
    const date = new Date(newDate)
    if (isNaN(date.getTime())) return

    onChange({
      operator: 'between',
      startDate: value?.startDate || new Date(),
      endDate: date
    })
  }

  const handlePresetClick = (preset: typeof datePresets[0]) => {
    const date = preset.getValue()

    if (operator === 'between') {
      const endDate = new Date()
      onChange({
        operator: 'between',
        startDate: date,
        endDate
      })
    } else {
      onChange({
        operator: operator === 'equals' ? 'gte' : operator,
        value: date
      })
    }
  }



  const formatDisplayDate = (date: string | Date | undefined) => {
    if (!date) return ''
    const d = typeof date === 'string' ? new Date(date) : date
    return d.toLocaleDateString()
  }

  const formatValueForBadge = (val: DateFilterType | undefined) => {
    if (!val) return ''

    if (val.operator === 'between') {
      return `${formatDisplayDate(val.startDate)} - ${formatDisplayDate(val.endDate)}`
    }

    return `${operatorLabels[val.operator]} ${formatDisplayDate(val.value)}`
  }

  return (
    <BaseFilter
      label={label}
      value={value}
      onChange={onChange as (value: unknown) => void}
      showValueBadge={true}
      valueFormatter={formatValueForBadge as (value: unknown) => string}
    >
      <div className="space-y-2">
        <Select value={operator} onValueChange={handleOperatorChange}>
          <SelectTrigger className="h-10 md:h-8 touch-target">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(operatorLabels).map(([op, label]) => (
              <SelectItem key={op} value={op}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {operator === 'between' ? (
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2">
              <div className="relative">
                <Input
                  type="date"
                  value={formatDateForInput(value?.startDate)}
                  onChange={(e) => handleStartDateChange(e.target.value)}
                  className="h-10 md:h-8 touch-target"
                />
              </div>
              <div className="relative">
                <Input
                  type="date"
                  value={formatDateForInput(value?.endDate)}
                  onChange={(e) => handleEndDateChange(e.target.value)}
                  className="h-10 md:h-8 touch-target"
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="relative">
            <Input
              type="date"
              value={formatDateForInput(value?.value)}
              onChange={(e) => handleValueChange(e.target.value)}
              className="h-10 md:h-8 touch-target"
            />
          </div>
        )}

        {/* Date Presets */}
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground">Quick presets:</div>
          <div className="flex flex-wrap gap-1">
            {datePresets.map((preset) => (
              <Button
                key={preset.label}
                variant="outline"
                size="sm"
                onClick={() => handlePresetClick(preset)}
                className="h-8 md:h-6 px-3 md:px-2 text-sm md:text-xs touch-target"
              >
                {preset.label}
              </Button>
            ))}
          </div>
        </div>

        {value && (
          <div className="text-xs text-muted-foreground flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            {operator === 'between'
              ? `${formatDisplayDate(value.startDate)} - ${formatDisplayDate(value.endDate)}`
              : `${operatorLabels[operator]} ${formatDisplayDate(value.value)}`
            }
          </div>
        )}
      </div>
    </BaseFilter>
  )
}
