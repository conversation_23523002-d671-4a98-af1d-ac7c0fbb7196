import React, { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { NumberFilter as NumberFilterType, NumberOperator } from '@/lib/filter-types'
import { handleNumberInputChange } from '@/lib/utils'

interface NumberRangeFilterProps {
  label: string
  value?: NumberFilterType
  onChange: (value: NumberFilterType | undefined) => void
  placeholder?: string
  prefix?: string
  suffix?: string
  min?: number
  max?: number
  step?: number
}

const operatorLabels: Record<NumberOperator, string> = {
  equals: 'Equals',
  gt: 'Greater than',
  gte: 'Greater than or equal',
  lt: 'Less than',
  lte: 'Less than or equal',
  between: 'Between'
}

export function NumberRangeFilter({
  label,
  value,
  onChange,
  placeholder,
  prefix = '₱',
  suffix = '',
  min = 0,
  max = 1000000,
  step = 1
}: NumberRangeFilterProps) {
  const [operator, setOperator] = useState<NumberOperator>(value?.operator || 'gte')

  const handleOperatorChange = (newOperator: NumberOperator) => {
    setOperator(newOperator)

    if (newOperator === 'between') {
      onChange({
        operator: newOperator,
        min: value?.min || min,
        max: value?.max || max
      })
    } else {
      onChange({
        operator: newOperator,
        value: value?.value || min
      })
    }
  }

  const handleValueChange = (newValue: string) => {
    const numValue = parseFloat(newValue)
    if (isNaN(numValue)) {
      onChange(undefined)
      return
    }

    if (operator === 'between') {
      onChange({
        operator,
        min: value?.min || min,
        max: value?.max || max,
        value: numValue
      })
    } else {
      onChange({
        operator,
        value: numValue
      })
    }
  }

  const handleMinChange = (newMin: string) => {
    const numMin = parseFloat(newMin)
    if (isNaN(numMin)) return

    onChange({
      operator: 'between',
      min: numMin,
      max: value?.max || max
    })
  }

  const handleMaxChange = (newMax: string) => {
    const numMax = parseFloat(newMax)
    if (isNaN(numMax)) return

    onChange({
      operator: 'between',
      min: value?.min || min,
      max: numMax
    })
  }

  const clearFilter = () => {
    onChange(undefined)
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <Label className="text-xs font-medium">{label}</Label>
        {value && (
          <button
            onClick={clearFilter}
            className="text-xs text-muted-foreground hover:text-foreground"
          >
            Clear
          </button>
        )}
      </div>

      <div className="space-y-2">
        <Select value={operator} onValueChange={handleOperatorChange}>
          <SelectTrigger className="h-10 md:h-8 touch-target">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(operatorLabels).map(([op, label]) => (
              <SelectItem key={op} value={op}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {operator === 'between' ? (
          <div className="grid grid-cols-2 gap-2">
            <div className="relative">
              {prefix && (
                <span className="absolute left-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                  {prefix}
                </span>
              )}
              <Input
                type="number"
                placeholder="Min"
                value={value?.min || ''}
                onChange={(e) => handleMinChange(e.target.value)}
                className={`h-10 md:h-8 touch-target ${prefix ? 'pl-6' : ''} ${suffix ? 'pr-8' : ''}`}
                min={min}
                max={max}
                step={step}
              />
              {suffix && (
                <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                  {suffix}
                </span>
              )}
            </div>
            <div className="relative">
              {prefix && (
                <span className="absolute left-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                  {prefix}
                </span>
              )}
              <Input
                type="number"
                placeholder="Max"
                value={value?.max || ''}
                onChange={(e) => handleMaxChange(e.target.value)}
                className={`h-10 md:h-8 touch-target ${prefix ? 'pl-6' : ''} ${suffix ? 'pr-8' : ''}`}
                min={min}
                max={max}
                step={step}
              />
              {suffix && (
                <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                  {suffix}
                </span>
              )}
            </div>
          </div>
        ) : (
          <div className="relative">
            {prefix && (
              <span className="absolute left-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                {prefix}
              </span>
            )}
            <Input
              type="number"
              placeholder={placeholder || `Enter ${label.toLowerCase()}...`}
              value={value?.value || ''}
              onChange={(e) => handleValueChange(e.target.value)}
              className={`h-10 md:h-8 touch-target ${prefix ? 'pl-6' : ''} ${suffix ? 'pr-8' : ''}`}
              min={min}
              max={max}
              step={step}
            />
            {suffix && (
              <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                {suffix}
              </span>
            )}
          </div>
        )}

        {value && (
          <div className="text-xs text-muted-foreground">
            {operator === 'between'
              ? `${prefix}${value.min || min} - ${prefix}${value.max || max}${suffix}`
              : `${operatorLabels[operator]} ${prefix}${value.value}${suffix}`
            }
          </div>
        )}
      </div>
    </div>
  )
}
