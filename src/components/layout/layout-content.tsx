'use client'

import { usePathname } from 'next/navigation'
import { AnimatedBottomNav } from '@/components/navigation/animated-bottom-nav'
import { FloatingActionButton } from '@/components/navigation/floating-action-button'
import { TopNav } from '@/components/navigation/top-nav'

interface LayoutContentProps {
  children: React.ReactNode
}

// Helper function to check if current route is an authentication page
function isAuthPage(pathname: string): boolean {
  return pathname.startsWith('/auth/')
}

export function LayoutContent({ children }: LayoutContentProps) {
  const pathname = usePathname()
  const isAuthRoute = isAuthPage(pathname)

  if (isAuthRoute) {
    // Authentication pages: Hide all navigation for clean, focused experience
    return (
      <main className="flex-1">
        {children}
      </main>
    )
  }

  // Regular app pages: Show full navigation
  return (
    <>
      <TopNav />
      <main className="flex-1 container mx-auto px-4 pb-16 pt-4">
        {children}
      </main>
      <AnimatedBottomNav />
      <FloatingActionButton />
    </>
  )
}
