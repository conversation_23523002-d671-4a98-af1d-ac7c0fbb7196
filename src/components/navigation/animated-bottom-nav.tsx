'use client'

import { usePathname, useRouter } from 'next/navigation'
import { ShoppingBag, Package, List, Plus, FileText } from 'lucide-react'
import { useAppStore } from '@/lib/store'
import { NavigationItem } from './navigation-item'
import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useBottomNavScroll } from '@/hooks/use-scroll'

const navItems = [
  {
    href: '/orders',
    label: 'Orders',
    icon: List,
  },
  {
    href: '/buy-list',
    label: 'Buy List',
    icon: ShoppingBag,
  },
  {
    href: '/packing',
    label: 'Packing',
    icon: Package,
  },
  {
    href: '/invoices',
    label: 'Invoices',
    icon: FileText,
  },
]

// Context-aware button configuration (same as original)
function getContextAwareAction(pathname: string, storeCodes: any[]) {
  const pathSegments = pathname.split('/').filter(Boolean)
  const basePath = `/${pathSegments[0] || ''}`

  switch (basePath) {
    case '/orders':
      return {
        href: '/orders/new',
        label: 'Add Order',
        action: 'menu',
        options: [
          {
            href: '/orders/new',
            label: 'Single Order',
            description: 'Add one order'
          },
          {
            href: '/orders/new/multi',
            label: 'Multiple Orders',
            description: 'Add multiple orders at once'
          }
        ]
      }
    case '/packing':
      return {
        href: '/orders/new',
        label: 'Add Order',
        action: 'navigate'
      }
    case '/customers':
      if (pathSegments.length > 1 && !isNaN(Number(pathSegments[1]))) {
        const customerId = pathSegments[1]
        return {
          href: `/orders/new?customerId=${customerId}`,
          label: 'Add Order for Customer',
          action: 'navigate'
        }
      }
      return {
        href: '/customers/new',
        label: 'Add Customer',
        action: 'navigate'
      }
    case '/stores':
      return {
        href: '/stores/new',
        label: 'Add Store',
        action: 'navigate'
      }
    case '/buy-list':
      if (pathSegments.length > 1) {
        const storeCode = pathSegments[1]
        return {
          href: `/orders/new?storeCode=${storeCode}`,
          label: 'Add Order for Store',
          action: 'navigate'
        }
      }
      return {
        href: '/orders/new',
        label: 'Add Order',
        action: 'navigate'
      }
    case '/invoices':
      return {
        href: '/invoices/new',
        label: 'Add Invoice',
        action: 'navigate'
      }
    default:
      return {
        href: '/orders/new',
        label: 'Add Order',
        action: 'navigate'
      }
  }
}

// Helper function to check if current route should hide bottom navigation
function shouldHideBottomNavOnRoute(pathname: string): boolean {
  // Hide on authentication pages
  if (pathname.startsWith('/auth/')) {
    return true
  }
  // Hide on order detail pages (/orders/[id])
  const orderDetailPattern = /^\/orders\/\d+$/
  if (orderDetailPattern.test(pathname)) {
    return true
  }

  // Hide on order form pages
  // - Add New Order page (/orders/new)
  // - Add Multiple Orders page (/orders/new/multi)
  const newOrderPattern = /^\/orders\/new(\/multi)?$/
  if (newOrderPattern.test(pathname)) {
    return true
  }

  // Hide on edit order pages (/orders/[id]/edit)
  const editOrderPattern = /^\/orders\/\d+\/edit$/
  if (editOrderPattern.test(pathname)) {
    return true
  }

  // Hide on packing detail pages (/packing/[id])
  const packingDetailPattern = /^\/packing\/\d+$/
  if (packingDetailPattern.test(pathname)) {
    return true
  }

  // Hide on customer management pages
  // - Customer detail/view page (/customers/[id])
  // - Add new customer page (/customers/new)
  // - Edit customer page (/customers/[id]/edit)
  const customerDetailPattern = /^\/customers\/\d+$/
  const customerNewPattern = /^\/customers\/new$/
  const customerEditPattern = /^\/customers\/\d+\/edit$/
  if (customerDetailPattern.test(pathname) || customerNewPattern.test(pathname) || customerEditPattern.test(pathname)) {
    return true
  }

  // Hide on store management pages
  // - Store detail/view page (/stores/[id])
  // - Add new store page (/stores/new)
  // - Edit store page (/stores/[id]/edit)
  const storeDetailPattern = /^\/stores\/\d+$/
  const storeNewPattern = /^\/stores\/new$/
  const storeEditPattern = /^\/stores\/\d+\/edit$/
  if (storeDetailPattern.test(pathname) || storeNewPattern.test(pathname) || storeEditPattern.test(pathname)) {
    return true
  }

  return false
}

export function AnimatedBottomNav() {
  const pathname = usePathname()
  const router = useRouter()
  const { storeCodes } = useAppStore()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  // Use simplified bottom navigation scroll logic
  const { shouldShowBottomNav } = useBottomNavScroll()

  // Check if current route should hide bottom navigation
  const shouldHideOnRoute = shouldHideBottomNavOnRoute(pathname)

  // Combined visibility logic:
  // - Hide completely on detail pages (route-based)
  // - Show ONLY when at top of page (scrollY <= 30px) on other pages
  // - Hide when scrolled down (scrollY > 30px)
  // - Does NOT reappear when scrolling back up unless reaching the very top
  const isVisible = !shouldHideOnRoute && shouldShowBottomNav

  // Get context-aware action configuration
  const contextAction = getContextAwareAction(pathname, storeCodes)

  const handleCenterButtonClick = (e: React.MouseEvent) => {
    e.preventDefault()

    if (contextAction.action === 'navigate') {
      router.push(contextAction.href)
    } else if (contextAction.action === 'menu') {
      setIsMenuOpen(!isMenuOpen)
    }
  }

  const handleMenuItemClick = (href: string) => {
    setIsMenuOpen(false)
    router.push(href)
  }

  return (
    <nav
      className={`
        fixed bottom-0 left-0 right-0 z-40 bg-background border-t
        transition-all duration-300 ease-in-out
        ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}
      `}
      style={{
        willChange: 'transform, opacity',
        backfaceVisibility: 'hidden',
        WebkitBackfaceVisibility: 'hidden'
      }}
    >
      <div className="container mx-auto px-4">
        {/* 5-element layout with center + button */}
        <div className="flex items-center justify-evenly py-2 min-h-[60px]">
          {/* First navigation item */}
          <div className="flex-1 max-w-[80px] flex justify-center">
            <NavigationItem
              href={navItems[0].href}
              label={navItems[0].label}
              icon={navItems[0].icon}
              variant="bottom-nav"
            />
          </div>

          {/* Second navigation item */}
          <div className="flex-1 max-w-[80px] flex justify-center">
            <NavigationItem
              href={navItems[1].href}
              label={navItems[1].label}
              icon={navItems[1].icon}
              variant="bottom-nav"
            />
          </div>

          {/* Center Add Button - Context-aware */}
          <div className="flex-1 max-w-[80px] flex justify-center">
            {contextAction.action === 'menu' ? (
              <Popover open={isMenuOpen} onOpenChange={setIsMenuOpen}>
                <PopoverTrigger asChild>
                  <button
                    className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-14 h-14 hover:bg-primary/90 active:bg-primary/80 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    title={contextAction.label}
                    aria-label={contextAction.label}
                  >
                    <Plus className="h-7 w-7" />
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-64 p-2" align="center" side="top">
                  <div className="space-y-1">
                    {contextAction.options?.map((option) => (
                      <button
                        key={option.href}
                        onClick={() => handleMenuItemClick(option.href)}
                        className="w-full text-left p-3 rounded-md hover:bg-accent transition-colors"
                      >
                        <div className="font-medium text-sm">{option.label}</div>
                        <div className="text-xs text-muted-foreground">{option.description}</div>
                      </button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
            ) : (
              <button
                onClick={handleCenterButtonClick}
                className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-14 h-14 hover:bg-primary/90 active:bg-primary/80 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                title={contextAction.label}
                aria-label={contextAction.label}
              >
                <Plus className="h-7 w-7" />
              </button>
            )}
          </div>

          {/* Third navigation item */}
          <div className="flex-1 max-w-[80px] flex justify-center">
            <NavigationItem
              href={navItems[2].href}
              label={navItems[2].label}
              icon={navItems[2].icon}
              variant="bottom-nav"
            />
          </div>

          {/* Fourth navigation item */}
          <div className="flex-1 max-w-[80px] flex justify-center">
            <NavigationItem
              href={navItems[3].href}
              label={navItems[3].label}
              icon={navItems[3].icon}
              variant="bottom-nav"
            />
          </div>
        </div>
      </div>
    </nav>
  )
}
