'use client'

import { usePathname, useRouter } from 'next/navigation'
import { Plus } from 'lucide-react'
import { useAppStore } from '@/lib/store'
import { useState } from 'react'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useBottomNavScroll } from '@/hooks/use-scroll'

// Same context-aware logic as bottom nav
function getContextAwareAction(pathname: string, storeCodes: any[]) {
  const pathSegments = pathname.split('/').filter(Boolean)
  const basePath = `/${pathSegments[0] || ''}`

  switch (basePath) {
    case '/orders':
      return {
        href: '/orders/new',
        label: 'Add Order',
        action: 'menu',
        options: [
          {
            href: '/orders/new',
            label: 'Single Order',
            description: 'Add one order'
          },
          {
            href: '/orders/new/multi',
            label: 'Multiple Orders',
            description: 'Add multiple orders at once'
          }
        ]
      }
    case '/packing':
      return {
        href: '/orders/new',
        label: 'Add Order',
        action: 'navigate'
      }
    case '/customers':
      if (pathSegments.length > 1 && !isNaN(Number(pathSegments[1]))) {
        const customerId = pathSegments[1]
        return {
          href: `/orders/new?customerId=${customerId}`,
          label: 'Add Order for Customer',
          action: 'navigate'
        }
      }
      return {
        href: '/customers/new',
        label: 'Add Customer',
        action: 'navigate'
      }
    case '/stores':
      return {
        href: '/stores/new',
        label: 'Add Store',
        action: 'navigate'
      }
    case '/buy-list':
      if (pathSegments.length > 1) {
        const storeCode = pathSegments[1]
        return {
          href: `/orders/new?storeCode=${storeCode}`,
          label: 'Add Order for Store',
          action: 'navigate'
        }
      }
      return {
        href: '/orders/new',
        label: 'Add Order',
        action: 'navigate'
      }
    case '/invoices':
      return {
        href: '/invoices/new',
        label: 'Add Invoice',
        action: 'navigate'
      }
    default:
      return {
        href: '/orders/new',
        label: 'Add Order',
        action: 'navigate'
      }
  }
}

// Helper function to check if current route should hide FAB
function shouldHideFABOnRoute(pathname: string): boolean {
  // Hide on authentication pages
  if (pathname.startsWith('/auth/')) {
    return true
  }
  // Hide on order detail pages (/orders/[id])
  const orderDetailPattern = /^\/orders\/\d+$/
  if (orderDetailPattern.test(pathname)) {
    return true
  }

  // Hide on order form pages
  // - Add New Order page (/orders/new)
  // - Add Multiple Orders page (/orders/new/multi)
  const newOrderPattern = /^\/orders\/new(\/multi)?$/
  if (newOrderPattern.test(pathname)) {
    return true
  }

  // Hide on edit order pages (/orders/[id]/edit)
  const editOrderPattern = /^\/orders\/\d+\/edit$/
  if (editOrderPattern.test(pathname)) {
    return true
  }

  // Hide on packing detail pages (/packing/[id])
  const packingDetailPattern = /^\/packing\/\d+$/
  if (packingDetailPattern.test(pathname)) {
    return true
  }

  // Hide on customer management pages
  // - Customer detail/view page (/customers/[id])
  // - Add new customer page (/customers/new)
  // - Edit customer page (/customers/[id]/edit)
  const customerDetailPattern = /^\/customers\/\d+$/
  const customerNewPattern = /^\/customers\/new$/
  const customerEditPattern = /^\/customers\/\d+\/edit$/
  if (customerDetailPattern.test(pathname) || customerNewPattern.test(pathname) || customerEditPattern.test(pathname)) {
    return true
  }

  // Hide on store management pages
  // - Store detail/view page (/stores/[id])
  // - Add new store page (/stores/new)
  // - Edit store page (/stores/[id]/edit)
  const storeDetailPattern = /^\/stores\/\d+$/
  const storeNewPattern = /^\/stores\/new$/
  const storeEditPattern = /^\/stores\/\d+\/edit$/
  if (storeDetailPattern.test(pathname) || storeNewPattern.test(pathname) || storeEditPattern.test(pathname)) {
    return true
  }

  return false
}

export function FloatingActionButton() {
  const pathname = usePathname()
  const router = useRouter()
  const { storeCodes } = useAppStore()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  // Use simplified bottom navigation scroll logic
  const { shouldShowFAB } = useBottomNavScroll()

  // Check if current route should hide FAB
  const shouldHideOnRoute = shouldHideFABOnRoute(pathname)

  // Combined FAB visibility logic:
  // - Hide completely on detail pages (route-based)
  // - Hide when at top of page (scrollY <= 30px) - bottom nav is visible
  // - Show when scrolled down (scrollY > 30px) - bottom nav is hidden
  // - Remains visible when scrolling back up until reaching the very top
  const shouldShowFABCombined = !shouldHideOnRoute && shouldShowFAB

  // Get context-aware action configuration
  const contextAction = getContextAwareAction(pathname, storeCodes)

  const handleFABClick = (e: React.MouseEvent) => {
    e.preventDefault()

    if (contextAction.action === 'navigate') {
      router.push(contextAction.href)
    } else if (contextAction.action === 'menu') {
      setIsMenuOpen(!isMenuOpen)
    }
  }

  const handleMenuItemClick = (href: string) => {
    setIsMenuOpen(false)
    router.push(href)
  }

  if (!shouldShowFABCombined) {
    return null
  }

  return (
    <div
      className={`
        fixed bottom-6 right-6 z-50
        transition-all duration-300 ease-in-out
        ${shouldShowFABCombined ? 'translate-y-0 opacity-100 scale-100' : 'translate-y-4 opacity-0 scale-95'}
      `}
      style={{
        willChange: 'transform, opacity, scale',
        backfaceVisibility: 'hidden',
        WebkitBackfaceVisibility: 'hidden'
      }}
    >
      {contextAction.action === 'menu' ? (
        <Popover open={isMenuOpen} onOpenChange={setIsMenuOpen}>
          <PopoverTrigger asChild>
            <button
              onClick={(e) => {
                e.preventDefault()
                setIsMenuOpen(!isMenuOpen)
              }}
              className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-16 h-16 hover:bg-primary/90 active:bg-primary/80 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:scale-105 active:scale-95 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              title={contextAction.label}
              aria-label={contextAction.label}
              type="button"
            >
              <Plus className="h-8 w-8" />
            </button>
          </PopoverTrigger>
          <PopoverContent className="w-64 p-2 mb-4" align="end" side="top">
            <div className="space-y-1">
              {contextAction.options?.map((option) => (
                <button
                  key={option.href}
                  onClick={() => handleMenuItemClick(option.href)}
                  className="w-full text-left p-3 rounded-md hover:bg-accent transition-colors"
                >
                  <div className="font-medium text-sm">{option.label}</div>
                  <div className="text-xs text-muted-foreground">{option.description}</div>
                </button>
              ))}
            </div>
          </PopoverContent>
        </Popover>
      ) : (
        <button
          onClick={handleFABClick}
          className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-16 h-16 hover:bg-primary/90 active:bg-primary/80 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:scale-105 active:scale-95 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          title={contextAction.label}
          aria-label={contextAction.label}
          type="button"
        >
          <Plus className="h-8 w-8" />
        </button>
      )}
    </div>
  )
}
