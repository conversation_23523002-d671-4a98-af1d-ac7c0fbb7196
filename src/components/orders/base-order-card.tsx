'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { LuPackage } from 'react-icons/lu'
import { ImagePreview } from '@/components/ui/image-preview'
import { useLongPress } from '@/hooks/use-long-press'
import { animations } from '@/lib/animation-utils'
import {
  getStatusBadgeClasses,
  getStatusBorderClasses,
  getStatusBackgroundClasses,
  getStatusLabel
} from '@/lib/order-status'
import { getImageUrl } from '@/lib/utils/image-utils'

interface Order {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  resellerPrice: number // For compatibility
  isBought: boolean
  packingStatus: string
  imageFilename?: string | null
  storeCode?: {
    id: number
    code: string
    name: string | null
  } | null
  customer?: {
    id: number
    name: string
  } | null
}

interface BaseOrderCardProps {
  item: Order
  variant: 'default' | 'buy-list' | 'packing' | 'packing-compact' | 'customer-compact' | 'customer-display'
  isSelected: boolean
  isBulkMode: boolean
  onToggleSelection: (orderId: number) => void
  onLongPress: (orderId: number) => void
  onCardClick: (orderId: number, event: React.MouseEvent) => void
  actions?: React.ReactNode
  formatCurrency?: (amount: number) => string
  onStatusUpdate?: (orderId: number, field: 'isBought' | 'packingStatus', value: boolean | string) => void
}

export function BaseOrderCard({
  item,
  variant,
  isSelected,
  isBulkMode,
  onToggleSelection,
  onLongPress,
  onCardClick,
  actions
}: BaseOrderCardProps) {
  const [isPressed, setIsPressed] = useState(false)

  const [longPressHandlers, longPressState] = useLongPress(
    () => onLongPress(item.id),
    {
      threshold: 600,
      onStart: () => setIsPressed(true),
      onFinish: () => setIsPressed(false),
      onCancel: () => setIsPressed(false)
    }
  )

  const handleCardClick = (e: React.MouseEvent) => {
    // For display-only variant, prevent all interactions
    if (variant === 'customer-display') {
      e.preventDefault()
      e.stopPropagation()
      return
    }

    // Prevent navigation if clicking on interactive elements
    const target = e.target as HTMLElement
    if (target.closest('button') ||
        target.closest('[role="button"]') ||
        target.closest('input') ||
        target.closest('[data-radix-dialog-overlay]') ||
        target.closest('[data-radix-dialog-content]') ||
        target.closest('[data-slot="dialog-overlay"]') ||
        target.closest('[data-slot="dialog-content"]') ||
        target.closest('[data-slot="dialog-portal"]')) {
      e.preventDefault()
      e.stopPropagation()
      return
    }

    // If in bulk mode, toggle selection instead of navigating
    if (isBulkMode) {
      e.preventDefault()
      e.stopPropagation()
      onToggleSelection(item.id)
      return
    }

    // Prevent navigation if the long press is active
    if (longPressState.isLongPressed) {
      e.preventDefault()
      e.stopPropagation()
      return
    }

    onCardClick(item.id, e)
  }

  const handleCheckboxClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onToggleSelection(item.id)
  }



  // Get variant-specific styling
  const getVariantPadding = () => {
    switch (variant) {
      case 'buy-list':
        return 'p-2'
      case 'packing':
        return 'p-3'
      case 'packing-compact':
      case 'customer-compact':
      case 'customer-display':
        return 'p-2'
      default:
        return 'p-3'
    }
  }

  const getVariantSpacing = () => {
    switch (variant) {
      case 'packing':
        return 'space-x-3'
      case 'packing-compact':
      case 'customer-compact':
      case 'customer-display':
        return 'space-x-2'
      default:
        return 'space-x-2'
    }
  }

  const getImageSize = () => {
    switch (variant) {
      case 'packing-compact':
        return 'w-16 h-16'
      case 'customer-compact':
      case 'customer-display':
        return 'w-8 h-8'
      default:
        return 'w-12 h-12'
    }
  }

  const getImageStyling = () => {
    switch (variant) {
      case 'packing-compact':
        return 'rounded-lg shadow-sm border border-border/50'
      case 'customer-compact':
      case 'customer-display':
        return 'rounded'
      default:
        return 'rounded'
    }
  }

  const getPlaceholderIconSize = () => {
    switch (variant) {
      case 'packing-compact':
        return 'h-7 w-7'
      case 'customer-compact':
      case 'customer-display':
        return 'h-4 w-4'
      default:
        return 'h-5 w-5'
    }
  }

  // Status styling
  const statusBorderClasses = getStatusBorderClasses(item)
  const statusBgClasses = getStatusBackgroundClasses(item)

  // Get card styling based on variant
  const getCardClassName = () => {
    const baseClasses = `${getVariantPadding()} relative overflow-hidden ${statusBorderClasses}`

    if (variant === 'customer-display') {
      // Display-only variant: no cursor, hover, or press effects
      return `${baseClasses} ${statusBgClasses}`
    }

    // Interactive variants
    return `${baseClasses} cursor-pointer group select-none ${
      isPressed ? 'scale-[0.99] bg-accent/50 dark:bg-accent/30 z-10' : statusBgClasses
    } ${isSelected ? 'ring-2 ring-primary/40 bg-primary/10 dark:bg-primary/5 z-10' : ''} ${
      animations.card({ hover: true, press: true, selected: isSelected })
    }`
  }

  const getCardTitle = () => {
    if (variant === 'customer-display') {
      return `${item.productName} - Qty: ${item.quantity}${item.usageUnit ? ` ${item.usageUnit}` : ''}`
    }

    return variant === 'packing-compact' || variant === 'customer-compact'
      ? `Click to view details for ${item.productName} - Qty: ${item.quantity}${item.usageUnit ? ` ${item.usageUnit}` : ''}`
      : `Click to view details for ${item.productName} - Status: ${getStatusLabel(item)}`
  }

  const cardProps = variant === 'customer-display'
    ? { className: getCardClassName(), title: getCardTitle() }
    : {
        className: getCardClassName(),
        onClick: handleCardClick,
        title: getCardTitle(),
        ...longPressHandlers
      }

  return (
    <Card {...cardProps}>
      {variant === 'packing-compact' ? (
        /* Packing Compact variant: centered vertical layout */
        <div className="flex flex-col items-center gap-2 relative">
          {/* Checkbox for bulk selection - positioned absolutely in top-left */}
          {(isBulkMode || isSelected) && (
            <div className="absolute top-0 left-0">
              <Checkbox
                checked={isSelected}
                onCheckedChange={() => onToggleSelection(item.id)}
                onClick={handleCheckboxClick}
                aria-label={`Select ${item.productName}`}
              />
            </div>
          )}

          {/* Centered Item Image */}
          {item.imageFilename ? (
            <div className={`${getImageSize()} flex-shrink-0 mt-1`}>
              <ImagePreview
                src={getImageUrl(item.imageFilename) || ''}
                alt={item.productName}
                className={`${getImageSize()} object-cover ${getImageStyling()}`}
              />
            </div>
          ) : (
            <div className={`${getImageSize()} bg-muted/50 ${getImageStyling()} flex items-center justify-center flex-shrink-0 mt-1`}>
              <LuPackage className={`${getPlaceholderIconSize()} text-muted-foreground`} />
            </div>
          )}

          {/* Centered Product Details */}
          <div className="flex flex-col items-center gap-1 text-center w-full">
            <h3 className="font-medium text-sm truncate leading-tight w-full">
              {item.productName}
            </h3>
            <span className="text-xs text-muted-foreground">
              Qty: {item.quantity}{item.usageUnit ? ` ${item.usageUnit}` : ''}
            </span>
          </div>

          {/* Centered Pack Button */}
          {actions && (
            <div className="flex justify-center w-full">
              {actions}
            </div>
          )}
        </div>
      ) : (
        /* Other variants: horizontal layout */
        <div className={`flex items-start ${getVariantSpacing()}`}>
          {/* Checkbox for bulk selection - hidden for display-only variant */}
          {variant !== 'customer-display' && (isBulkMode || isSelected) && (
            <div className="flex items-center pt-1">
              <Checkbox
                checked={isSelected}
                onCheckedChange={() => onToggleSelection(item.id)}
                onClick={handleCheckboxClick}
                aria-label={`Select ${item.productName}`}
              />
            </div>
          )}

          {/* Item Image */}
          {item.imageFilename ? (
            <div className={`${getImageSize()} flex-shrink-0`}>
              <ImagePreview
                src={getImageUrl(item.imageFilename) || ''}
                alt={item.productName}
                className={`${getImageSize()} object-cover ${getImageStyling()}`}
              />
            </div>
          ) : (
            <div className={`${getImageSize()} bg-muted/50 ${getImageStyling()} flex items-center justify-center flex-shrink-0`}>
              <LuPackage className={`${getPlaceholderIconSize()} text-muted-foreground`} />
            </div>
          )}

          {/* Item Details */}
          <div className="flex-1 min-w-0">
            {variant === 'customer-compact' ? (
            /* Customer Compact variant: minimal layout with name, quantity and actions */
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-sm truncate">
                  {item.productName}
                </h3>
                <span className="text-xs text-muted-foreground">
                  Qty: {item.quantity}{item.usageUnit ? ` ${item.usageUnit}` : ''}
                </span>
              </div>
              {/* Actions positioned on the right for customer compact variant */}
              {actions && actions}
            </div>
          ) : variant === 'packing' ? (
            /* Packing variant: horizontal layout with actions on the right */
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <h3 className="font-medium text-base truncate flex-1">
                    {item.productName}
                  </h3>
                  {/* Status Badge */}
                  <Badge className={`text-xs px-2 py-0.5 h-5 flex-shrink-0 ${getStatusBadgeClasses(item)}`}>
                    {getStatusLabel(item, true)}
                  </Badge>
                </div>

                <div className="flex items-center gap-1.5 mt-0.5 flex-wrap">
                  <span className="text-xs text-muted-foreground">
                    Qty: {item.quantity}{item.usageUnit ? ` ${item.usageUnit}` : ''}
                  </span>
                  {item.storeCode && (
                    <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-5">
                      {item.storeCode.code}
                    </Badge>
                  )}
                  {item.customer && (
                    <Badge variant="secondary" className="text-xs px-1.5 py-0.5 h-5">
                      {item.customer.name}
                    </Badge>
                  )}
                </div>

                {item.comment && (
                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {item.comment}
                  </p>
                )}
              </div>

              {/* Actions positioned on the right for packing variant */}
              {actions && actions}
            </div>
          ) : (
            /* Default variant: vertical layout */
            <>
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2">
                    <h3 className="font-medium text-base truncate flex-1">
                      {item.productName}
                    </h3>
                    {/* Status Badge */}
                    <Badge className={`text-xs px-2 py-0.5 h-5 flex-shrink-0 ${getStatusBadgeClasses(item)}`}>
                      {getStatusLabel(item, true)}
                    </Badge>
                  </div>

                  <div className="flex items-center gap-1.5 mt-0.5 flex-wrap">
                    <span className="text-xs text-muted-foreground">
                      Qty: {item.quantity}{item.usageUnit ? ` ${item.usageUnit}` : ''}
                    </span>
                    {item.storeCode && (
                      <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-5">
                        {item.storeCode.code}
                      </Badge>
                    )}
                    {item.customer && (
                      <Badge variant="secondary" className="text-xs px-1.5 py-0.5 h-5">
                        {item.customer.name}
                      </Badge>
                    )}
                  </div>

                  {item.comment && (
                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                      {item.comment}
                    </p>
                  )}
                </div>
              </div>

              {/* Actions positioned below for default variant */}
              {actions && (
                <div className="flex items-center justify-end mt-1 gap-1.5">
                  {actions}
                </div>
              )}
            </>
            )}
          </div>
        </div>
      )}
    </Card>
  )
}
