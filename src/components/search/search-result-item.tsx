'use client'

import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { ArrowRightIcon, Store, Users } from 'lucide-react'

// Local type definition since search API is disabled during migration
interface SearchResult {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  imageFilename: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  isBought: boolean
  packingStatus: string
  storeCode?: {
    id: number
    code: string
    name: string | null
  } | null
  customer?: {
    id: number
    name: string
  } | null
  createdAt: string
  updatedAt: string
  matchType: 'productName' | 'customer' | 'storeCode' | 'usageUnit' | 'comment'
  matchText: string
}

interface SearchResultItemProps {
  item: SearchResult
  searchQuery: string
  onSelect?: () => void
}

function highlightText(text: string, query: string): React.ReactNode {
  if (!query.trim()) return text

  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  const parts = text.split(regex)

  return parts.map((part, index) =>
    regex.test(part) ? (
      <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 rounded px-0.5">
        {part}
      </mark>
    ) : (
      part
    )
  )
}

function getMatchTypeLabel(matchType: SearchResult['matchType']): string {
  switch (matchType) {
    case 'productName':
      return 'Product'
    case 'customer':
      return 'Customer'
    case 'storeCode':
      return 'Store'
    default:
      return 'Order'
  }
}

function getNavigationHint(matchType: SearchResult['matchType']): string {
  switch (matchType) {
    case 'productName':
      return 'View order details'
    case 'customer':
      return 'View customer details'
    case 'storeCode':
      return 'View store buy list'
    default:
      return 'View details'
  }
}

function getMatchTypeColor(matchType: SearchResult['matchType']): string {
  switch (matchType) {
    case 'productName':
      return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800/50'
    case 'customer':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-800/50'
    case 'storeCode':
      return 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800/50'
    default:
      return 'bg-muted/60 text-muted-foreground border-border dark:bg-muted/40 dark:text-muted-foreground'
  }
}

export function SearchResultItem({ item, searchQuery, onSelect }: SearchResultItemProps) {
  const router = useRouter()

  const handleClick = () => {
    // Smart navigation based on match type
    let targetUrl: string

    switch (item.matchType) {
      case 'productName':
        // Navigate to order details page
        targetUrl = `/orders/${item.id}`
        break

      case 'customer':
        // Navigate to customer page if customer exists
        if (item.customer) {
          targetUrl = `/customers/${item.customer.id}`
        } else {
          // Fallback to order details
          targetUrl = `/orders/${item.id}`
        }
        break

      case 'storeCode':
        // Navigate to store buy list if store code exists
        if (item.storeCode) {
          targetUrl = `/buy-list/${item.storeCode.code.toLowerCase()}`
        } else {
          // Fallback to order details
          targetUrl = `/orders/${item.id}`
        }
        break

      default:
        // Default fallback to order details
        targetUrl = `/orders/${item.id}`
    }

    router.push(targetUrl)
    onSelect?.()
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(price)
  }

  return (
    <div
      className="flex items-center gap-3 p-3 hover:bg-accent rounded-lg cursor-pointer transition-colors group"
      onClick={handleClick}
      title={getNavigationHint(item.matchType)}
    >
      {/* Item/Store/Reseller Image */}
      <div className="flex-shrink-0 w-12 h-12 bg-muted rounded-md overflow-hidden">
        {item.matchType === 'storeCode' ? (
          // Store icon for store results
          <div className="w-full h-full flex items-center justify-center text-muted-foreground">
            <Store className="h-6 w-6" />
          </div>
        ) : item.matchType === 'customer' ? (
          // Customer icon for customer results
          <div className="w-full h-full flex items-center justify-center text-muted-foreground">
            <Users className="h-6 w-6" />
          </div>
        ) : item.imageFilename ? (
          // Order image for order results
          <Image
            src={`/api/images/orders/${item.imageFilename}`}
            alt={item.productName}
            width={48}
            height={48}
            className="w-full h-full object-cover"
          />
        ) : (
          // No image placeholder
          <div className="w-full h-full flex items-center justify-center text-muted-foreground text-xs">
            No Image
          </div>
        )}
      </div>

      {/* Item Details */}
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 min-w-0">
            {item.matchType === 'storeCode' ? (
              // Store-specific display
              <>
                <h4 className="font-medium text-sm truncate">
                  {item.storeCode?.name || highlightText(item.storeCode?.code || '', searchQuery)}
                </h4>
                <div className="flex items-center gap-2 mt-1">
                  <Badge
                    variant="outline"
                    className={cn("text-xs", getMatchTypeColor(item.matchType))}
                  >
                    {getMatchTypeLabel(item.matchType)}
                  </Badge>
                  <span className="text-xs text-muted-foreground">
                    {highlightText(item.matchText, searchQuery)}
                  </span>
                </div>
              </>
            ) : item.matchType === 'customer' ? (
              // Customer-specific display
              <>
                <h4 className="font-medium text-sm truncate">
                  {highlightText(item.customer?.name || '', searchQuery)}
                </h4>
                <div className="flex items-center gap-2 mt-1">
                  <Badge
                    variant="outline"
                    className={cn("text-xs", getMatchTypeColor(item.matchType))}
                  >
                    {getMatchTypeLabel(item.matchType)}
                  </Badge>
                  <span className="text-xs text-muted-foreground">
                    {highlightText(item.matchText, searchQuery)}
                  </span>
                </div>
              </>
            ) : (
              // Regular item display
              <>
                <h4 className="font-medium text-sm truncate">
                  {highlightText(item.productName, searchQuery)}
                </h4>
                <div className="flex items-center gap-2 mt-1">
                  <Badge
                    variant="outline"
                    className={cn("text-xs", getMatchTypeColor(item.matchType))}
                  >
                    {getMatchTypeLabel(item.matchType)}
                  </Badge>
                  {item.matchType !== 'productName' && (
                    <span className="text-xs text-muted-foreground">
                      {highlightText(item.matchText, searchQuery)}
                    </span>
                  )}
                </div>
              </>
            )}
          </div>
          {item.matchType !== 'storeCode' && item.matchType !== 'customer' && (
            <div className="text-right flex-shrink-0">
              <div className="text-sm font-medium">
                {formatPrice(item.customerPrice)}
              </div>
              <div className="text-xs text-muted-foreground">
                Qty: {item.quantity}
              </div>
            </div>
          )}
        </div>

        {/* Additional Info */}
        <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
          {item.matchType === 'storeCode' ? (
            // Store-specific additional info
            <span>Store buy list</span>
          ) : item.matchType === 'customer' ? (
            // Customer-specific additional info
            <span>Customer details</span>
          ) : (
            // Regular order additional info
            <>
              {item.customer && (
                <span>
                  {highlightText(item.customer.name, searchQuery)}
                </span>
              )}
              {item.storeCode && (
                <>
                  {item.customer && <span>•</span>}
                  <span>
                    {highlightText(item.storeCode.code, searchQuery)}
                  </span>
                </>
              )}
              <span>•</span>
              <Badge
                variant={item.isBought ? "default" : "outline"}
                className="text-xs"
              >
                {item.isBought ? "Bought" : "To Buy"}
              </Badge>
            </>
          )}
        </div>
      </div>

      {/* Navigation Arrow */}
      <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
        <ArrowRightIcon className="h-4 w-4 text-muted-foreground" />
      </div>
    </div>
  )
}
