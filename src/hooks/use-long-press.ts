import { useCallback, useRef, useState } from 'react'

export interface LongPressOptions {
  threshold?: number // Duration in milliseconds for long press
  onStart?: () => void // Called when long press starts
  onFinish?: () => void // Called when long press completes
  onCancel?: () => void // Called when long press is cancelled
}

export interface LongPressHandlers {
  onMouseDown: (event: React.MouseEvent) => void
  onMouseUp: (event: React.MouseEvent) => void
  onMouseLeave: (event: React.MouseEvent) => void
  onTouchStart: (event: React.TouchEvent) => void
  onTouchEnd: (event: React.TouchEvent) => void
  onTouchCancel: (event: React.TouchEvent) => void
  onTouchMove: (event: React.TouchEvent) => void
}

export interface LongPressState {
  isPressed: boolean
  isLongPressed: boolean
}

export function useLongPress(
  callback: () => void,
  options: LongPressOptions = {}
): [LongPressHandlers, LongPressState] {
  const { threshold = 600, onStart, onFinish, onCancel } = options

  const [isPressed, setIsPressed] = useState(false)
  const [isLongPressed, setIsLongPressed] = useState(false)

  const timeout = useRef<NodeJS.Timeout | null>(null)
  const target = useRef<EventTarget | null>(null)
  const startPosition = useRef<{ x: number; y: number } | null>(null)

  const start = useCallback((event: React.MouseEvent | React.TouchEvent) => {
    // Safely prevent default to avoid text selection and context menus
    // Only call preventDefault if the event allows it (not passive)
    try {
      if (event.cancelable) {
        event.preventDefault()
      }
    } catch (e) {
      // Ignore errors from passive event listeners
    }

    // Store the target and initial position
    target.current = event.target as EventTarget

    if ('touches' in event) {
      startPosition.current = {
        x: event.touches[0].clientX,
        y: event.touches[0].clientY
      }
    } else {
      startPosition.current = {
        x: event.clientX,
        y: event.clientY
      }
    }

    setIsPressed(true)
    onStart?.()

    timeout.current = setTimeout(() => {
      setIsLongPressed(true)
      callback()
      onFinish?.()
    }, threshold)
  }, [callback, threshold, onStart, onFinish])

  const clear = useCallback((shouldCancel = true) => {
    if (timeout.current) {
      clearTimeout(timeout.current)
    }
    setIsPressed(false)

    if (shouldCancel && !isLongPressed) {
      onCancel?.()
    }

    // Reset long press state after a short delay to allow for visual feedback
    setTimeout(() => setIsLongPressed(false), 100)
  }, [isLongPressed, onCancel])

  const checkMovement = useCallback((currentX: number, currentY: number) => {
    if (!startPosition.current) return false

    const deltaX = Math.abs(currentX - startPosition.current.x)
    const deltaY = Math.abs(currentY - startPosition.current.y)
    const threshold = 10 // pixels

    return deltaX > threshold || deltaY > threshold
  }, [])

  return [
    {
      onMouseDown: useCallback((event: React.MouseEvent) => {
        // Only handle left mouse button
        if (event.button === 0) {
          start(event)
        }
      }, [start]),

      onMouseUp: useCallback((_event: React.MouseEvent) => {
        clear(false)
      }, [clear]),

      onMouseLeave: useCallback((_event: React.MouseEvent) => {
        clear(true)
      }, [clear]),

      onTouchStart: useCallback((event: React.TouchEvent) => {
        start(event)
      }, [start]),

      onTouchEnd: useCallback((_event: React.TouchEvent) => {
        clear(false)
      }, [clear]),

      onTouchCancel: useCallback((_event: React.TouchEvent) => {
        clear(true)
      }, [clear]),

      onTouchMove: useCallback((event: React.TouchEvent) => {
        if (event.touches.length > 0) {
          const touch = event.touches[0]
          if (checkMovement(touch.clientX, touch.clientY)) {
            clear(true)
          }
        }
      }, [clear, checkMovement])
    },
    {
      isPressed,
      isLongPressed
    }
  ]
}
