'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/auth-provider'

// Always use Appwrite - fully migrated
const USE_APPWRITE = true

interface UseDataOptions {
  endpoint: string // Legacy endpoint (not used)
  appwriteEndpoint: string // Required Appwrite endpoint
  dependencies?: any[]
  enabled?: boolean
}

interface UseDataResult<T> {
  data: T | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  mutate: (newData: T) => void
}

export function useAppwriteData<T>({
  endpoint,
  appwriteEndpoint,
  dependencies = [],
  enabled = true
}: UseDataOptions): UseDataResult<T> {
  const [data, setData] = useState<T | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { isAuthenticated } = useAuth()

  // Always use Appwrite endpoint
  const apiEndpoint = appwriteEndpoint

  const fetchData = async () => {
    if (!enabled) return

    // Require authentication for all Appwrite APIs
    if (!isAuthenticated) {
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(apiEndpoint)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const result = await response.json()
      setData(result)
    } catch (err: any) {
      setError(err.message || 'An error occurred')
      console.error(`Error fetching data from ${apiEndpoint}:`, err)
    } finally {
      setIsLoading(false)
    }
  }

  const mutate = (newData: T) => {
    setData(newData)
  }

  useEffect(() => {
    fetchData()
  }, [apiEndpoint, isAuthenticated, enabled, ...dependencies])

  return {
    data,
    isLoading,
    error,
    refetch: fetchData,
    mutate
  }
}

// Specific hooks for different data types
export function useStores() {
  return useAppwriteData<any[]>({
    endpoint: '', // Legacy - not used
    appwriteEndpoint: '/api/appwrite/store-codes'
  })
}

export function useCustomers() {
  return useAppwriteData<any[]>({
    endpoint: '', // Legacy - not used
    appwriteEndpoint: '/api/appwrite/customers'
  })
}

export function useOrders() {
  return useAppwriteData<any[]>({
    endpoint: '', // Legacy - not used
    appwriteEndpoint: '/api/appwrite/orders'
  })
}

export function useBuyList() {
  return useAppwriteData<any[]>({
    endpoint: '', // Legacy - not used
    appwriteEndpoint: '/api/appwrite/orders?type=buy-list'
  })
}

export function usePackingList() {
  return useAppwriteData<any[]>({
    endpoint: '', // Legacy - not used
    appwriteEndpoint: '/api/appwrite/orders?type=packing-list'
  })
}

export function useInvoices() {
  return useAppwriteData<any[]>({
    endpoint: '', // Legacy - not used
    appwriteEndpoint: '/api/appwrite/invoices'
  })
}

export function useStore(id: string) {
  return useAppwriteData<any>({
    endpoint: '', // Legacy - not used
    appwriteEndpoint: `/api/appwrite/store-codes/${id}`,
    dependencies: [id],
    enabled: !!id
  })
}

export function useCustomer(id: string) {
  return useAppwriteData<any>({
    endpoint: '', // Legacy - not used
    appwriteEndpoint: `/api/appwrite/customers/${id}`,
    dependencies: [id],
    enabled: !!id
  })
}

export function useOrder(id: string) {
  return useAppwriteData<any>({
    endpoint: '', // Legacy - not used
    appwriteEndpoint: `/api/appwrite/orders/${id}`,
    dependencies: [id],
    enabled: !!id
  })
}

export function useInvoice(id: string) {
  return useAppwriteData<any>({
    endpoint: '', // Legacy - not used
    appwriteEndpoint: `/api/appwrite/invoices/${id}`,
    dependencies: [id],
    enabled: !!id
  })
}

// Mutation hooks for creating/updating data
export function useCreateStore() {
  const { isAuthenticated } = useAuth()

  return async (data: any) => {
    if (!isAuthenticated) {
      throw new Error('Authentication required')
    }

    const response = await fetch('/api/appwrite/store-codes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    return response.json()
  }
}

export function useUpdateStore() {
  const { isAuthenticated } = useAuth()

  return async (id: string, data: any) => {
    if (!isAuthenticated) {
      throw new Error('Authentication required')
    }

    const response = await fetch(`/api/appwrite/store-codes/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    return response.json()
  }
}

export function useDeleteStore() {
  const { isAuthenticated } = useAuth()

  return async (id: string) => {
    if (!isAuthenticated) {
      throw new Error('Authentication required')
    }

    const response = await fetch(`/api/appwrite/store-codes/${id}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    return response.json()
  }
}

export function useCreateCustomer() {
  const { isAuthenticated } = useAuth()

  return async (data: any) => {
    if (!isAuthenticated) {
      throw new Error('Authentication required')
    }

    const response = await fetch('/api/appwrite/customers', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    return response.json()
  }
}

export function useCreateOrder() {
  const { isAuthenticated } = useAuth()

  return async (data: any) => {
    if (!isAuthenticated) {
      throw new Error('Authentication required')
    }

    const response = await fetch('/api/appwrite/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    return response.json()
  }
}

export function useBuyOrder() {
  const { isAuthenticated } = useAuth()

  return async (id: string, data: { storePrice: number; pasabuyFee: number; customerPrice: number }) => {
    if (!isAuthenticated) {
      throw new Error('Authentication required')
    }

    const response = await fetch(`/api/appwrite/orders/${id}/buy`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    return response.json()
  }
}

export function usePackOrder() {
  const { isAuthenticated } = useAuth()

  return async (id: string) => {
    if (!isAuthenticated) {
      throw new Error('Authentication required')
    }

    const response = await fetch(`/api/appwrite/orders/${id}/pack`, {
      method: 'POST',
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    return response.json()
  }
}
