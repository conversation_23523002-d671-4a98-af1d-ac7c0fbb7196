'use client'

import { useState } from 'react'
import { useAuth } from '@/components/providers/auth-provider'

interface UploadResult {
  fileId: string
  filename: string
  url: string
  size: number
  mimeType: string
}

interface UploadOptions {
  folder?: string
  maxSize?: number
  allowedTypes?: string[]
}

interface UseUploadResult {
  upload: (file: File, options?: UploadOptions) => Promise<UploadResult>
  deleteFile: (fileId: string) => Promise<void>
  isUploading: boolean
  error: string | null
}

export function useAppwriteUpload(): UseUploadResult {
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { isAuthenticated } = useAuth()

  const upload = async (file: File, options?: UploadOptions): Promise<UploadResult> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required')
    }

    setIsUploading(true)
    setError(null)

    try {
      const formData = new FormData()
      formData.append('image', file)
      
      if (options?.folder) {
        formData.append('folder', options.folder)
      }

      const response = await fetch('/api/appwrite/upload', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Upload failed')
      }

      return {
        fileId: result.fileId,
        filename: result.filename,
        url: result.url,
        size: result.size,
        mimeType: result.mimeType
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Upload failed'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setIsUploading(false)
    }
  }

  const deleteFile = async (fileId: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Authentication required')
    }

    setError(null)

    try {
      const response = await fetch(`/api/appwrite/upload?fileId=${encodeURIComponent(fileId)}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Delete failed')
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Delete failed'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }

  return {
    upload,
    deleteFile,
    isUploading,
    error
  }
}

// Specific upload hooks for different use cases
export function useOrderImageUpload() {
  return useAppwriteUpload()
}

export function useProductImageUpload() {
  const upload = useAppwriteUpload()
  
  return {
    ...upload,
    uploadProductImage: (file: File) => upload.upload(file, { 
      folder: 'products',
      maxSize: 10,
      allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    })
  }
}

export function useInvoiceAttachmentUpload() {
  const upload = useAppwriteUpload()
  
  return {
    ...upload,
    uploadAttachment: (file: File) => upload.upload(file, { 
      folder: 'invoices',
      maxSize: 5,
      allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'application/pdf']
    })
  }
}
