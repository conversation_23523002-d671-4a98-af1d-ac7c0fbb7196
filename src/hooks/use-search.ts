import { useState, useEffect, useCallback } from 'react'

// Local type definition since search API is disabled during migration
interface SearchResult {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  imageFilename: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  isBought: boolean
  packingStatus: string
  storeCode?: {
    id: number
    code: string
    name: string | null
  } | null
  customer?: {
    id: number
    name: string
  } | null
  createdAt: string
  updatedAt: string
  matchType: 'productName' | 'customer' | 'storeCode' | 'usageUnit' | 'comment'
  matchText: string
}

interface GroupedSearchResults {
  productName: SearchResult[]
  customer: SearchResult[]
  storeCode: SearchResult[]
  usageUnit: SearchResult[]
  comment: SearchResult[]
  total: number
}

interface UseSearchOptions {
  debounceMs?: number
  minQueryLength?: number
}

interface UseSearchReturn {
  query: string
  setQuery: (query: string) => void
  results: GroupedSearchResults | null
  isLoading: boolean
  error: string | null
  clearResults: () => void
}

export function useSearch(options: UseSearchOptions = {}): UseSearchReturn {
  const { debounceMs = 300, minQueryLength = 1 } = options
  
  const [query, setQuery] = useState('')
  const [debouncedQuery, setDebouncedQuery] = useState('')
  const [results, setResults] = useState<GroupedSearchResults | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Debounce the query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [query, debounceMs])

  // Perform search when debounced query changes
  useEffect(() => {
    const performSearch = async () => {
      if (debouncedQuery.length < minQueryLength) {
        setResults(null)
        setIsLoading(false)
        setError(null)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        // Search API is disabled during Appwrite migration
        // Return empty results for now
        const data: GroupedSearchResults = {
          productName: [],
          customer: [],
          storeCode: [],
          usageUnit: [],
          comment: [],
          total: 0
        }
        setResults(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        setResults(null)
      } finally {
        setIsLoading(false)
      }
    }

    performSearch()
  }, [debouncedQuery, minQueryLength])

  const clearResults = useCallback(() => {
    setQuery('')
    setDebouncedQuery('')
    setResults(null)
    setError(null)
    setIsLoading(false)
  }, [])

  return {
    query,
    setQuery,
    results,
    isLoading,
    error,
    clearResults
  }
}
