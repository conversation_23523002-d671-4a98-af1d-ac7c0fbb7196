import { appwriteStorageService } from '../services/appwrite-storage-service'

/**
 * Utility functions for handling image URLs in the migration from local storage to Appwrite
 */

/**
 * Get the appropriate image URL based on whether it's a legacy local file or Appwrite file ID
 * @param imageIdentifier - Either a legacy filename or Appwrite file ID
 * @param width - Optional width for Appwrite preview
 * @param height - Optional height for Appwrite preview
 * @param quality - Optional quality for Appwrite preview (1-100)
 * @returns The appropriate image URL
 */
export function getImageUrl(
  imageIdentifier: string | null | undefined,
  width?: number,
  height?: number,
  quality?: number
): string | null {
  if (!imageIdentifier) {
    return null
  }

  // Check if it's a legacy local file (contains file extension)
  const isLegacyFile = imageIdentifier.includes('.') && 
    (imageIdentifier.includes('-') || imageIdentifier.includes('_'))

  if (isLegacyFile) {
    // Legacy local file - use the old API route
    return `/api/images/orders/${imageIdentifier}`
  } else {
    // Appwrite file ID - use Appwrite storage service
    if (width || height || quality) {
      // Use preview URL for resized images
      return appwriteStorageService.getFilePreview(imageIdentifier, width, height, quality)
    } else {
      // Use direct file URL
      return appwriteStorageService.getFileUrl(imageIdentifier)
    }
  }
}

/**
 * Get a thumbnail/preview URL for an image
 * @param imageIdentifier - Either a legacy filename or Appwrite file ID
 * @param size - Thumbnail size (defaults to 200x200)
 * @param quality - Image quality (defaults to 80)
 * @returns The thumbnail URL
 */
export function getImageThumbnail(
  imageIdentifier: string | null | undefined,
  size: number = 200,
  quality: number = 80
): string | null {
  return getImageUrl(imageIdentifier, size, size, quality)
}

/**
 * Get a responsive image URL for different screen sizes
 * @param imageIdentifier - Either a legacy filename or Appwrite file ID
 * @param breakpoint - Screen size breakpoint ('sm', 'md', 'lg', 'xl')
 * @returns The responsive image URL
 */
export function getResponsiveImageUrl(
  imageIdentifier: string | null | undefined,
  breakpoint: 'sm' | 'md' | 'lg' | 'xl' = 'md'
): string | null {
  if (!imageIdentifier) {
    return null
  }

  // Define responsive sizes
  const sizes = {
    sm: { width: 400, height: 400 },
    md: { width: 600, height: 600 },
    lg: { width: 800, height: 800 },
    xl: { width: 1200, height: 1200 }
  }

  const { width, height } = sizes[breakpoint]
  return getImageUrl(imageIdentifier, width, height, 85)
}

/**
 * Check if an image identifier is a legacy local file
 * @param imageIdentifier - The image identifier to check
 * @returns True if it's a legacy file, false if it's an Appwrite file ID
 */
export function isLegacyImage(imageIdentifier: string | null | undefined): boolean {
  if (!imageIdentifier) {
    return false
  }

  return imageIdentifier.includes('.') && 
    (imageIdentifier.includes('-') || imageIdentifier.includes('_'))
}

/**
 * Get a placeholder image URL for when no image is available
 * @param width - Image width
 * @param height - Image height
 * @param text - Optional text to display on placeholder
 * @returns Placeholder image URL
 */
export function getPlaceholderImageUrl(
  width: number = 400,
  height: number = 400,
  text?: string
): string {
  const baseUrl = 'https://placehold.co'
  const textParam = text ? `/${encodeURIComponent(text)}` : ''
  return `${baseUrl}/${width}x${height}/e2e8f0/64748b${textParam}`
}

/**
 * Get image URL with fallback to placeholder
 * @param imageIdentifier - Either a legacy filename or Appwrite file ID
 * @param width - Image width for placeholder
 * @param height - Image height for placeholder
 * @param placeholderText - Text for placeholder
 * @returns Image URL or placeholder URL
 */
export function getImageUrlWithFallback(
  imageIdentifier: string | null | undefined,
  width: number = 400,
  height: number = 400,
  placeholderText?: string
): string {
  const imageUrl = getImageUrl(imageIdentifier)
  
  if (imageUrl) {
    return imageUrl
  }
  
  return getPlaceholderImageUrl(width, height, placeholderText)
}

/**
 * Migrate a legacy image filename to Appwrite storage
 * This function would be used in a migration script
 * @param legacyFilename - The legacy filename to migrate
 * @returns Promise with the new Appwrite file ID
 */
export async function migrateLegacyImage(legacyFilename: string): Promise<string | null> {
  try {
    // This would be implemented in a migration script
    // For now, just return null to indicate migration needed
    console.warn(`Legacy image migration needed for: ${legacyFilename}`)
    return null
  } catch (error) {
    console.error(`Failed to migrate legacy image ${legacyFilename}:`, error)
    return null
  }
}

/**
 * Delete an image from storage (works for both legacy and Appwrite)
 * @param imageIdentifier - Either a legacy filename or Appwrite file ID
 * @returns Promise indicating success
 */
export async function deleteImage(imageIdentifier: string): Promise<boolean> {
  try {
    if (isLegacyImage(imageIdentifier)) {
      // For legacy images, we can't delete them through the API
      // They would need to be cleaned up manually or through a cleanup script
      console.warn(`Cannot delete legacy image through API: ${imageIdentifier}`)
      return false
    } else {
      // Delete Appwrite file
      await appwriteStorageService.deleteFile(imageIdentifier)
      return true
    }
  } catch (error) {
    console.error(`Failed to delete image ${imageIdentifier}:`, error)
    return false
  }
}
