import { NextRequest } from 'next/server'
import { Account, Client as NodeClient } from 'node-appwrite'
import { server<PERSON>lient } from './appwrite-server'
import { config } from 'dotenv'

// Load environment variables
config()

class ServerAuthService {
  private account: Account

  constructor() {
    this.account = new Account(serverClient)
  }

  // Create a session-only client (no API key) to avoid conflicts
  private createSessionClient(sessionToken: string): NodeClient {
    return new NodeClient()
      .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || '')
      .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '')
      .setSession(sessionToken)
  }

  // Extract session from request cookies
  private getSessionFromRequest(request: NextRequest): string | null {
    // Appwrite stores session in cookies with specific naming pattern
    const cookies = request.cookies

    // Look for Appwrite session cookie
    // The cookie name follows pattern: a_session_{projectId}
    const projectId = process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID
    const sessionCookieName = `a_session_${projectId}`

    const sessionCookie = cookies.get(sessionCookieName)
    if (sessionCookie) {
      return sessionCookie.value
    }

    // Fallback: look for any session cookie that might be Appwrite-related
    const cookieNames = Array.from(cookies.getAll().map(cookie => cookie.name))
    for (const name of cookieNames) {
      if (name.startsWith('a_session_')) {
        const cookie = cookies.get(name)
        if (cookie) {
          return cookie.value
        }
      }
    }

    return null
  }

  // Get user ID from request
  async getUserIdFromRequest(request: NextRequest): Promise<string | null> {
    try {
      const sessionToken = this.getSessionFromRequest(request)
      if (!sessionToken) {
        return null
      }

      // Create a session-only client to avoid API key conflicts
      const sessionClient = this.createSessionClient(sessionToken)
      const sessionAccount = new Account(sessionClient)
      const user = await sessionAccount.get()
      return user.$id
    } catch (error) {
      console.error('Server auth error:', error)
      return null
    }
  }

  // Validate session from request
  async validateSession(request: NextRequest): Promise<boolean> {
    try {
      const userId = await this.getUserIdFromRequest(request)
      return !!userId
    } catch (error) {
      return false
    }
  }

  // Get user from request
  async getUserFromRequest(request: NextRequest) {
    try {
      const sessionToken = this.getSessionFromRequest(request)
      if (!sessionToken) {
        return null
      }

      // Create a session-only client to avoid API key conflicts
      const sessionClient = this.createSessionClient(sessionToken)
      const sessionAccount = new Account(sessionClient)
      const user = await sessionAccount.get()
      return user
    } catch (error) {
      console.error('Server get user error:', error)
      return null
    }
  }
}

// Create and export server auth service instance
export const serverAuthService = new ServerAuthService()

// Helper function for API routes to get user ID
export async function getUserIdFromRequest(request: NextRequest): Promise<string | null> {
  return serverAuthService.getUserIdFromRequest(request)
}

// Helper function for API routes to validate authentication
export async function requireAuth(request: NextRequest): Promise<string> {
  const userId = await getUserIdFromRequest(request)
  if (!userId) {
    throw new Error('Authentication required')
  }
  return userId
}
