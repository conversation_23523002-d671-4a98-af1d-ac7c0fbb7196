import { account } from './appwrite'
import { ID } from 'appwrite'

export interface User {
  $id: string
  name: string
  email: string
  emailVerification: boolean
  phone: string
  phoneVerification: boolean
  prefs: Record<string, any>
  registration: string
  status: boolean
  labels: string[]
  accessedAt: string
  $createdAt: string
  $updatedAt: string
}

export interface Session {
  $id: string
  userId: string
  expire: string
  provider: string
  providerUid: string
  providerAccessToken: string
  providerAccessTokenExpiry: string
  providerRefreshToken: string
  ip: string
  osCode: string
  osName: string
  osVersion: string
  clientType: string
  clientCode: string
  clientName: string
  clientVersion: string
  clientEngine: string
  clientEngineVersion: string
  deviceName: string
  deviceBrand: string
  deviceModel: string
  countryCode: string
  countryName: string
  current: boolean
  $createdAt: string
  $updatedAt: string
}

class AuthService {
  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      const user = await account.get()
      return user as User
    } catch (error) {
      return null
    }
  }

  // Get current session
  async getCurrentSession(): Promise<Session | null> {
    try {
      const session = await account.getSession('current')
      return session as Session
    } catch (error) {
      return null
    }
  }

  // Register new user
  async register(email: string, password: string, name: string): Promise<User> {
    try {
      const user = await account.create(ID.unique(), email, password, name)
      
      // Automatically log in after registration
      await this.login(email, password)
      
      return user as User
    } catch (error: any) {
      throw new Error(error.message || 'Registration failed')
    }
  }

  // Login user
  async login(email: string, password: string): Promise<Session> {
    try {
      const session = await account.createEmailPasswordSession(email, password)
      return session as Session
    } catch (error: any) {
      throw new Error(error.message || 'Login failed')
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      await account.deleteSession('current')
    } catch (error: any) {
      throw new Error(error.message || 'Logout failed')
    }
  }

  // Logout from all sessions
  async logoutAll(): Promise<void> {
    try {
      await account.deleteSessions()
    } catch (error: any) {
      throw new Error(error.message || 'Logout failed')
    }
  }

  // Send password recovery email
  async sendPasswordRecovery(email: string): Promise<void> {
    try {
      await account.createRecovery(
        email,
        `${window.location.origin}/auth/reset-password`
      )
    } catch (error: any) {
      throw new Error(error.message || 'Password recovery failed')
    }
  }

  // Complete password recovery
  async completePasswordRecovery(
    userId: string,
    secret: string,
    password: string
  ): Promise<void> {
    try {
      await account.updateRecovery(userId, secret, password)
    } catch (error: any) {
      throw new Error(error.message || 'Password reset failed')
    }
  }

  // Send email verification
  async sendEmailVerification(): Promise<void> {
    try {
      await account.createVerification(
        `${window.location.origin}/auth/verify-email`
      )
    } catch (error: any) {
      throw new Error(error.message || 'Email verification failed')
    }
  }

  // Complete email verification
  async completeEmailVerification(userId: string, secret: string): Promise<void> {
    try {
      await account.updateVerification(userId, secret)
    } catch (error: any) {
      throw new Error(error.message || 'Email verification failed')
    }
  }

  // Update user name
  async updateName(name: string): Promise<User> {
    try {
      const user = await account.updateName(name)
      return user as User
    } catch (error: any) {
      throw new Error(error.message || 'Name update failed')
    }
  }

  // Update user email
  async updateEmail(email: string, password: string): Promise<User> {
    try {
      const user = await account.updateEmail(email, password)
      return user as User
    } catch (error: any) {
      throw new Error(error.message || 'Email update failed')
    }
  }

  // Update user password
  async updatePassword(newPassword: string, oldPassword: string): Promise<User> {
    try {
      const user = await account.updatePassword(newPassword, oldPassword)
      return user as User
    } catch (error: any) {
      throw new Error(error.message || 'Password update failed')
    }
  }

  // Update user phone
  async updatePhone(phone: string, password: string): Promise<User> {
    try {
      const user = await account.updatePhone(phone, password)
      return user as User
    } catch (error: any) {
      throw new Error(error.message || 'Phone update failed')
    }
  }

  // Update user preferences
  async updatePreferences(prefs: Record<string, any>): Promise<User> {
    try {
      const user = await account.updatePrefs(prefs)
      return user as User
    } catch (error: any) {
      throw new Error(error.message || 'Preferences update failed')
    }
  }

  // Get user preferences
  async getPreferences(): Promise<Record<string, any>> {
    try {
      const user = await this.getCurrentUser()
      return user?.prefs || {}
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get preferences')
    }
  }

  // List user sessions
  async getSessions(): Promise<Session[]> {
    try {
      const sessions = await account.listSessions()
      return sessions.sessions as Session[]
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get sessions')
    }
  }

  // Delete specific session
  async deleteSession(sessionId: string): Promise<void> {
    try {
      await account.deleteSession(sessionId)
    } catch (error: any) {
      throw new Error(error.message || 'Failed to delete session')
    }
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      const user = await this.getCurrentUser()
      return !!user
    } catch (error) {
      return false
    }
  }

  // Get user ID
  async getUserId(): Promise<string | null> {
    try {
      const user = await this.getCurrentUser()
      return user?.$id || null
    } catch (error) {
      return null
    }
  }

  // Create anonymous session (for guest users)
  async createAnonymousSession(): Promise<Session> {
    try {
      const session = await account.createAnonymousSession()
      return session as Session
    } catch (error: any) {
      throw new Error(error.message || 'Anonymous session creation failed')
    }
  }

  // Convert anonymous account to regular account
  async convertAnonymousAccount(email: string, password: string): Promise<User> {
    try {
      const user = await account.updateEmail(email, password)
      return user as User
    } catch (error: any) {
      throw new Error(error.message || 'Account conversion failed')
    }
  }

  // Send password reset email
  async sendPasswordResetEmail(email: string): Promise<void> {
    try {
      const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/auth/reset-password`
      await account.createRecovery(email, resetUrl)
    } catch (error: any) {
      throw new Error(error.message || 'Failed to send password reset email')
    }
  }

  // Reset password with secret
  async resetPassword(userId: string, secret: string, password: string): Promise<void> {
    try {
      await account.updateRecovery(userId, secret, password)
    } catch (error: any) {
      throw new Error(error.message || 'Password reset failed')
    }
  }
}

// Create and export auth service instance
export const authService = new AuthService()

// Export auth utilities
export { ID } from 'appwrite'
