// Appwrite Collection: filterPresets
//
// This service manages user-defined filter presets.
//
// Key Fields for Indexing in Appwrite:
// - userId: (Required for data isolation and querying by user)
// - name: (Optional, if you want to ensure names are unique per user or query by name)
//
// Security Rules Reminder:
// - Ensure Appwrite collection security rules are set up so that users can only manage
//   their OWN filter presets (e.g., based on `userId`).
//
import { ID, Query, Models } from 'node-appwrite';
import { serverDatabases } from '@/lib/appwrite-server'; // Server-side Appwrite client
import { DATABASE_ID } from '@/lib/appwrite'; // Config constants

// Note: FILTER_PRESETS_COLLECTION_ID needs to be defined in appwrite.ts
const FILTER_PRESETS_COLLECTION_ID = 'filter_presets'
import { FilterConfig, SortConfig } from '@/lib/filter-types';

// Schema for filterPresets documents:
// - name: String (required)
// - description: String (optional)
// - filters: JSON object (FilterConfig)
// - sort: JSON object (SortConfig)
// - userId: String (creator/owner, indexed, required for data isolation)
// - createdAt: DateTime (automatically handled by Appwrite or set by service)
// - updatedAt: DateTime (automatically handled by Appwrite or set by service)

export interface FilterPresetDocument extends Models.Document {
  name: string;
  description?: string | null;
  filters: FilterConfig; // Appwrite stores JSON as strings, but SDK handles parsing
  sort: SortConfig;     // Same as above
  userId: string;
  // createdAt and updatedAt are typically $createdAt and $updatedAt from Appwrite
}

// For create/update, we might not have $id, etc.
export type FilterPresetData = Omit<FilterPresetDocument, '$id' | '$collectionId' | '$databaseId' | '$createdAt' | '$updatedAt' | '$permissions' | 'userId'>;


const appwriteFilterPresetService = {
    async getFilterPresets(userId: string): Promise<FilterPresetDocument[]> {
        console.log(`Attempting to get filter presets for user: ${userId}`);
        // Mock implementation:
        // In a real scenario:
        // const response = await databases.listDocuments(
        //     DB_ID,
        //     FILTER_PRESETS_COLLECTION_ID,
        //     [Query.equal('userId', userId)]
        // );
        // return response.documents as FilterPresetDocument[];
        const mockPresets: FilterPresetDocument[] = [
            { $id: 'preset1', name: 'My Default View', filters: { isBought: false }, sort: { columns: [{ field: 'createdAt', direction: 'desc', priority: 0 }] }, userId, $collectionId: FILTER_PRESETS_COLLECTION_ID, $databaseId: DATABASE_ID, $createdAt: new Date().toISOString(), $updatedAt: new Date().toISOString(), $permissions: [] },
            { $id: 'preset2', name: 'High Priority Items', filters: { search: 'urgent' }, sort: { columns: [{ field: 'productName', direction: 'asc', priority: 0 }] }, userId, $collectionId: FILTER_PRESETS_COLLECTION_ID, $databaseId: DATABASE_ID, $createdAt: new Date().toISOString(), $updatedAt: new Date().toISOString(), $permissions: [] },
        ];
        console.log('Mock fetched filter presets:', mockPresets);
        return mockPresets;
    },

    async getFilterPresetById(id: string, userId: string): Promise<FilterPresetDocument | null> {
        console.log(`Attempting to get filter preset by ID: ${id} for user: ${userId}`);
        // Mock implementation:
        // In a real scenario:
        // const preset = await databases.getDocument(DB_ID, FILTER_PRESETS_COLLECTION_ID, id);
        // if (preset.userId !== userId) throw new Error('Unauthorized or preset not found');
        // return preset as FilterPresetDocument;
        const mockPreset: FilterPresetDocument = { $id: id, name: 'Specific Preset', filters: {}, sort: { columns: [] }, userId, $collectionId: FILTER_PRESETS_COLLECTION_ID, $databaseId: DATABASE_ID, $createdAt: new Date().toISOString(), $updatedAt: new Date().toISOString(), $permissions: [] };
        console.log('Mock fetched filter preset by ID:', mockPreset);
        return userId === mockPreset.userId ? mockPreset : null;
    },

    async createFilterPreset(data: FilterPresetData, userId: string): Promise<FilterPresetDocument> {
        console.log('Attempting to create filter preset with data:', data, 'for user:', userId);
        if (!data.name) {
            throw new Error('Preset name is required.');
        }
        const presetToCreate = {
            ...data,
            userId,
            // filters and sort should be stringified if not handled by SDK, but Node SDK usually handles JSON objects.
        };
        // Mock implementation:
        // return await databases.createDocument(DB_ID, FILTER_PRESETS_COLLECTION_ID, ID.unique(), presetToCreate) as FilterPresetDocument;
        const mockCreatedPreset: FilterPresetDocument = {
            $id: ID.unique(),
            name: data.name,
            description: data.description,
            filters: data.filters,
            sort: data.sort,
            userId: userId,
            $collectionId: FILTER_PRESETS_COLLECTION_ID,
            $databaseId: DATABASE_ID,
            $createdAt: new Date().toISOString(),
            $updatedAt: new Date().toISOString(),
            $permissions: [],
        };
        console.log('Mock created filter preset:', mockCreatedPreset);
        return mockCreatedPreset;
    },

    async updateFilterPreset(id: string, data: Partial<FilterPresetData>, userId: string): Promise<FilterPresetDocument> {
        console.log(`Attempting to update filter preset ID: ${id} with data:`, data, 'for user:', userId);
        // Mock implementation:
        // In a real scenario, first get the document to ensure user has permission
        // const existingPreset = await this.getFilterPresetById(id, userId);
        // if (!existingPreset) throw new Error('Preset not found or unauthorized');
        // return await databases.updateDocument(DB_ID, FILTER_PRESETS_COLLECTION_ID, id, data) as FilterPresetDocument;
         const mockExistingPreset: FilterPresetDocument = { $id: id, name: 'Old Name', filters: {}, sort: { columns: [] }, userId, $collectionId: FILTER_PRESETS_COLLECTION_ID, $databaseId: DATABASE_ID, $createdAt: new Date(Date.now() - 100000).toISOString(), $updatedAt: new Date(Date.now() - 50000).toISOString(), $permissions: [] };
        if (mockExistingPreset.userId !== userId) throw new Error('Mock Unauthorized');

        const mockUpdatedPreset: FilterPresetDocument = {
            ...mockExistingPreset,
            ...data,
            $updatedAt: new Date().toISOString(),
        };
        console.log('Mock updated filter preset:', mockUpdatedPreset);
        return mockUpdatedPreset;
    },

    async deleteFilterPreset(id: string, userId: string): Promise<void> {
        console.log(`Attempting to delete filter preset ID: ${id} for user: ${userId}`);
        // Mock implementation:
        // In a real scenario, first get the document to ensure user has permission (or rely on Appwrite permissions)
        // const existingPreset = await this.getFilterPresetById(id, userId);
        // if (!existingPreset) throw new Error('Preset not found or unauthorized');
        // await databases.deleteDocument(DB_ID, FILTER_PRESETS_COLLECTION_ID, id);
        console.log('Mock deleted filter preset ID:', id);
        return;
    },
};

export { appwriteFilterPresetService };
