// Appwrite Collection: storePricing
//
// This service manages specific pricing rules for items or categories within certain stores,
// or general price adjustments for a store.
//
// Key Fields for Indexing in Appwrite:
// - userId: (Required for data isolation and querying by user)
// - storeCodeId: (Required, to link to the store this rule applies to)
// - itemId: (Optional, for item-specific rules)
// - category: (Optional, for category-specific rules)
// - isEnabled: (Optional, to quickly filter active/inactive rules)
//
// Security Rules Reminder:
// - Ensure Appwrite collection security rules are set up so that users can only create,
//   read, update, and delete their OWN pricing rules (e.g., based on `userId`).
// - Admins might have broader access.
//
import { ID, Query, Models } from 'node-appwrite'; // Import Models
import { serverDatabases } from '@/lib/appwrite-server'; // Server-side Appwrite client
import { DATABASE_ID } from '@/lib/appwrite'; // Config constants

// Note: STORE_PRICING_COLLECTION_ID needs to be defined in appwrite.ts
const STORE_PRICING_COLLECTION_ID = 'store_pricing'

// Schema for storePricing documents:
// - storeCodeId: String (document ID of the store code it links to, indexed, required)
// - storeName: String (denormalized for easier display, optional)
// - itemId: String (optional, for item-specific pricing)
// - itemName: String (optional, denormalized)
// - category: String (optional, for category-specific pricing)
// - price: Number (the special price for this store/item/category, required)
// - originalPrice: Number (optional, the standard price before this rule)
// - cost: Number (optional)
// - margin: Number (optional, can be calculated if price and cost are present)
// - discountPercentage: Number (optional)
// - discountAmount: Number (optional)
// - validFrom: Datetime (ISO string, optional)
// - validTo: Datetime (ISO string, optional)
// - notes: String (optional)
// - isEnabled: Boolean (defaults to true)
// - userId: String (creator/owner of this pricing rule, indexed, required)

export interface StorePricingRule extends Models.Document { // Extend Models.Document for Appwrite fields
    storeCodeId: string;
    storeName?: string | null;
    itemId?: string | null;
    itemName?: string | null;
    category?: string | null;
    price: number;
    originalPrice?: number | null;
    cost?: number | null;
    margin?: number | null;
    discountPercentage?: number | null;
    discountAmount?: number | null;
    validFrom?: string | null; // ISO Datetime string
    validTo?: string | null;   // ISO Datetime string
    notes?: string | null;
    isEnabled: boolean;
    userId: string;
    // $createdAt and $updatedAt are inherited from Models.Document as required strings
}

// Ensure data conforms to what Appwrite expects (e.g., null for optional empty strings)
function sanitizeInputData(data: Partial<StorePricingRule>): any {
    const sanitized: any = { ...data };
    for (const key in sanitized) {
        if (sanitized[key] === undefined) {
            sanitized[key] = null;
        }
        // Appwrite might prefer null over empty strings for optional text fields
        if (typeof sanitized[key] === 'string' && sanitized[key].trim() === '' &&
            ['storeName', 'itemId', 'itemName', 'category', 'notes', 'validFrom', 'validTo'].includes(key)) {
            sanitized[key] = null;
        }
    }
    return sanitized;
}


const appwriteStorePricingService = {
    async createStorePricingRule(data: Omit<StorePricingRule, '$id' | '$createdAt' | '$updatedAt' | 'isEnabled'> & { isEnabled?: boolean }, userId: string): Promise<StorePricingRule> {
        console.log('Attempting to create store pricing rule with data:', data, 'for user:', userId);
        const ruleData = sanitizeInputData({
            ...data,
            userId,
            isEnabled: data.isEnabled === undefined ? true : data.isEnabled,
        });

        // Basic validation before sending to Appwrite
        if (!ruleData.storeCodeId || typeof ruleData.price !== 'number') {
            throw new Error('storeCodeId and price are required to create a pricing rule.');
        }

        // Mock implementation:
        // In a real scenario, this would be:
        // return await serverDatabases.createDocument(DATABASE_ID, STORE_PRICING_COLLECTION_ID, ID.unique(), ruleData);
        const mockRule: StorePricingRule = {
            $id: ID.unique(),
            $collectionId: STORE_PRICING_COLLECTION_ID,
            $databaseId: DATABASE_ID,
            $permissions: [],
            ...ruleData,
            isEnabled: ruleData.isEnabled,
            $createdAt: new Date().toISOString(),
            $updatedAt: new Date().toISOString(),
        };
        console.log('Mock created store pricing rule:', mockRule);
        return mockRule;
    },

    async getStorePricingRuleById(id: string, userId: string): Promise<StorePricingRule | null> {
        console.log('Attempting to get store pricing rule by ID:', id, 'for user:', userId);
        // Mock implementation:
        // In a real scenario:
        // const rule = await serverDatabases.getDocument(DATABASE_ID, STORE_PRICING_COLLECTION_ID, id);
        // if (rule.userId !== userId) throw new Error('Unauthorized'); // Or handle via Appwrite permissions
        // return rule as StorePricingRule;
        const mockRule: StorePricingRule = {
            $id: id,
            $collectionId: STORE_PRICING_COLLECTION_ID,
            $databaseId: DATABASE_ID,
            $permissions: [],
            storeCodeId: 'STORE001', // Example storeCodeId
            price: 100, // Example price
            isEnabled: true,
            userId: userId,
            storeName: 'Mock Store Detailed',
            notes: 'This is a detailed mock rule for editing.',
            $createdAt: new Date().toISOString(),
            $updatedAt: new Date().toISOString(),
            // Fields expected by EditStorePricingPage that are not in StorePricingRule schema
            // We add them to the mock object for compatibility with the form
            name: 'Mock Config Name from Service', // Corresponds to StorePricing.name
            serviceFee: 25, // Corresponds to StorePricing.serviceFee
            pricingTiers: [ // Corresponds to StorePricing.pricingTiers
                { id: 1, minPrice: 0, maxPrice: 500, markupType: 'PERCENTAGE', markupValue: 10, pasabuyFee: 5, sortOrder: 0 },
                { id: 2, minPrice: 500.01, maxPrice: null, markupType: 'FIXED_AMOUNT', markupValue: 60, pasabuyFee: 10, sortOrder: 1 },
            ],
            // storeCode is also part of StorePricing, but storeCodeId is the key link
            storeCode: { id: 123, code: 'STORE001', name: 'Mock Store Detailed Name' }, // Mocked storeCode object
        } as any; // Use 'as any' for mock to include extra fields for the form
        console.log('Mock fetched store pricing rule by ID (with extra fields for edit page):', mockRule);
        return userId === mockRule.userId ? mockRule : null;
    },

    async updateStorePricingRule(id: string, data: Partial<Omit<StorePricingRule, '$id' | 'userId' | '$createdAt' | '$updatedAt'>>, userId: string): Promise<StorePricingRule> {
        console.log('Attempting to update store pricing rule ID:', id, 'with data:', data, 'for user:', userId);

        // Data here is Partial<StorePricingRule>, but the form sends StorePricing-like data.
        // The API route for PUT /store-pricing/[id] will need to transform the form's body
        // into what this service expects (e.g., putting tiers/serviceFee into 'notes').
        const ruleData = sanitizeInputData(data);

        const mockUpdatedRule: StorePricingRule = {
            $id: id,
            $collectionId: STORE_PRICING_COLLECTION_ID,
            $databaseId: DATABASE_ID,
            $permissions: [],
            storeCodeId: ruleData.storeCodeId || 'STORE001',
            price: ruleData.price === undefined ? 100 : ruleData.price, // If 'price' comes in data, use it
            notes: ruleData.notes || 'Updated notes with tiers/fees if transformed by API route',
            isEnabled: ruleData.isEnabled === undefined ? true : ruleData.isEnabled,
            userId: userId,
            storeName: ruleData.storeName || 'Mock Store Updated',
            // Ensure other fields from ruleData are spread if they are part of StorePricingRule
            ...ruleData,
            $createdAt: new Date(Date.now() - 100000).toISOString(),
            $updatedAt: new Date().toISOString(),
        };
        console.log('Mock updated store pricing rule:', mockUpdatedRule);
        return mockUpdatedRule;
    },

    async deleteStorePricingRule(id: string, userId: string): Promise<void> {
        console.log('Attempting to delete store pricing rule ID:', id, 'for user:', userId);
        // Mock implementation:
        // In a real scenario, first get the document to ensure user has permission (or rely on Appwrite permissions)
        // const existingRule = await this.getStorePricingRuleById(id, userId);
        // if (!existingRule) throw new Error('Rule not found or unauthorized');
        // await serverDatabases.deleteDocument(DATABASE_ID, STORE_PRICING_COLLECTION_ID, id);
        console.log('Mock deleted store pricing rule ID:', id);
        return;
    },

    async getStorePricingRules(userId: string, storeCodeId?: string, itemId?: string, category?: string): Promise<StorePricingRule[]> {
        console.log('Attempting to get store pricing rules for user:', userId, { storeCodeId, itemId, category });
        const queries: string[] = [Query.equal('userId', userId)];

        if (storeCodeId) {
            queries.push(Query.equal('storeCodeId', storeCodeId));
        }
        if (itemId) {
            queries.push(Query.equal('itemId', itemId));
        }
        if (category) {
            queries.push(Query.equal('category', category));
        }

        // Mock implementation:
        // In a real scenario:
        // const response = await serverDatabases.listDocuments(DATABASE_ID, STORE_PRICING_COLLECTION_ID, queries);
        // return response.documents as StorePricingRule[];
        const mockRules: StorePricingRule[] = [
            {
                $id: 'rule1',
                $collectionId: STORE_PRICING_COLLECTION_ID,
                $databaseId: DATABASE_ID,
                $permissions: [],
                storeCodeId: storeCodeId || 'STORE001',
                price: 90,
                isEnabled: true,
                userId,
                storeName: 'Mock Store A',
                category: category || 'Default',
                $createdAt: new Date().toISOString(),
                $updatedAt: new Date().toISOString()
            },
            {
                $id: 'rule2',
                $collectionId: STORE_PRICING_COLLECTION_ID,
                $databaseId: DATABASE_ID,
                $permissions: [],
                storeCodeId: storeCodeId || 'STORE002',
                price: 150,
                isEnabled: true,
                userId,
                storeName: 'Mock Store B',
                itemId: itemId || 'ITEM00X',
                $createdAt: new Date().toISOString(),
                $updatedAt: new Date().toISOString()
            },
        ];
        console.log('Mock fetched store pricing rules:', mockRules, 'with queries:', queries);
        // Simple filtering for mock
        return mockRules.filter(rule =>
            (!storeCodeId || rule.storeCodeId === storeCodeId) &&
            (!itemId || rule.itemId === itemId) &&
            (!category || rule.category === category)
        );
    },
};

export { appwriteStorePricingService };
