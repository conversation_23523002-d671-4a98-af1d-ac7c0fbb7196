// Appwrite Collection: pricingSettings
//
// This service manages global pricing settings for a user.
// Each user should have at most one document in this collection.
//
// Key Fields for Indexing in Appwrite:
// - userId: (Required, should be unique or used as document ID for user-specific settings)
//
// Security Rules Reminder:
// - Ensure Appwrite collection security rules are set up so that users can only manage
//   their OWN pricing settings document (e.g., based on `userId`).
//
import { ID, Query, Models } from 'node-appwrite';
import { serverDatabases } from '@/lib/appwrite-server'; // Server-side Appwrite client
import { DATABASE_ID } from '@/lib/appwrite'; // Config constants

// Note: PRICING_SETTINGS_COLLECTION_ID needs to be defined in appwrite.ts
const PRICING_SETTINGS_COLLECTION_ID = 'pricing_settings'

// Schema based on src/app/pricing-settings/page.tsx
// - markupType: String (enum: 'PERCENTAGE', 'FIXED_AMOUNT')
// - markupValue: Number
// - serviceFee: Number
// - userId: String (unique, links to the user)
// - (Appwrite will add $id, $createdAt, $updatedAt, $permissions)

export interface PricingSettingsDocument extends Models.Document {
  markupType: 'PERCENTAGE' | 'FIXED_AMOUNT';
  markupValue: number;
  serviceFee: number;
  userId: string;
}

// Data for creating/updating (userId will be handled by service methods)
export type PricingSettingsData = Omit<PricingSettingsDocument, '$id' | '$collectionId' | '$databaseId' | '$createdAt' | '$updatedAt' | '$permissions' | 'userId'>;


const appwritePricingSettingsService = {
    /**
     * Fetches the pricing settings for a given user.
     * Since there should be at most one settings document per user,
     * this fetches the first one found.
     */
    async getPricingSettings(userId: string): Promise<PricingSettingsDocument | null> {
        console.log(`Attempting to get pricing settings for user: ${userId}`);
        try {
            // In a real scenario:
            // const response = await serverDatabases.listDocuments(
            //     DATABASE_ID,
            //     PRICING_SETTINGS_COLLECTION_ID,
            //     [Query.equal('userId', userId), Query.limit(1)]
            // );
            // if (response.documents.length > 0) {
            //     return response.documents[0] as PricingSettingsDocument;
            // }
            // return null;

            // Mock implementation:
            const mockSettings: PricingSettingsDocument = {
                $id: 'mock_settings_id_for_' + userId,
                $collectionId: PRICING_SETTINGS_COLLECTION_ID,
                $databaseId: DATABASE_ID,
                $createdAt: new Date().toISOString(),
                $updatedAt: new Date().toISOString(),
                $permissions: [],
                userId: userId,
                markupType: 'PERCENTAGE',
                markupValue: 100, // Default 100%
                serviceFee: 20,   // Default P20
            };
            console.log('Mock fetched pricing settings:', mockSettings);
            return mockSettings;

        } catch (error) {
            console.error('Error fetching pricing settings:', error);
            // Depending on Appwrite behavior, "not found" might throw or return empty list.
            // If it throws a specific "not found" error, rethrow or handle it.
            // For now, assume null if error or not found.
            return null;
        }
    },

    /**
     * Creates or updates the pricing settings for a given user.
     * Appwrite doesn't have a direct "upsert" with unique constraint on userId in one go.
     * This stub will simulate it: try to get, then update or create.
     */
    async updatePricingSettings(userId: string, data: PricingSettingsData): Promise<PricingSettingsDocument> {
        console.log(`Attempting to update pricing settings for user: ${userId} with data:`, data);

        // Basic validation
        if (typeof data.markupValue !== 'number' || typeof data.serviceFee !== 'number' ||
            !['PERCENTAGE', 'FIXED_AMOUNT'].includes(data.markupType)) {
            throw new Error('Invalid data for pricing settings.');
        }

        // In a real scenario:
        // const existingSettings = await this.getPricingSettings(userId);
        // if (existingSettings) {
        //     console.log(`Updating existing settings (ID: ${existingSettings.$id}) for user ${userId}`);
        //     return await serverDatabases.updateDocument(
        //         DATABASE_ID,
        //         PRICING_SETTINGS_COLLECTION_ID,
        //         existingSettings.$id,
        //         data // Only fields to update
        //     ) as PricingSettingsDocument;
        // } else {
        //     console.log(`Creating new settings for user ${userId}`);
        //     return await serverDatabases.createDocument(
        //         DATABASE_ID,
        //         PRICING_SETTINGS_COLLECTION_ID,
        //         ID.unique(), // Or a specific ID if userId is to be the document ID
        //         { ...data, userId }
        //     ) as PricingSettingsDocument;
        // }

        // Mock implementation:
        const mockUpdatedSettings: PricingSettingsDocument = {
            $id: 'mock_settings_id_for_' + userId, // Assume stable ID for mock
            $collectionId: PRICING_SETTINGS_COLLECTION_ID,
            $databaseId: DATABASE_ID,
            $createdAt: new Date(Date.now() - 100000).toISOString(), // Older created date
            $updatedAt: new Date().toISOString(),
            $permissions: [],
            userId: userId,
            markupType: data.markupType,
            markupValue: data.markupValue,
            serviceFee: data.serviceFee,
        };
        console.log('Mock updated/created pricing settings:', mockUpdatedSettings);
        return mockUpdatedSettings;
    },
};

export { appwritePricingSettingsService };
