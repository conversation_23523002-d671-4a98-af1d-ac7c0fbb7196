import { 
  serverDatabases, 
  databases, 
  DATABASE_ID, 
  COLLECTIONS, 
  convertFromAppwriteDocument,
  convertToAppwriteDocument,
  Query,
  Permission
} from '../appwrite'
import { ID } from 'appwrite'
import { authService } from '../auth'

export interface Customer {
  id: string
  name: string
  customerNumber?: string
  customerType?: string
  status?: string
  email?: string
  phone?: string
  alternatePhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  businessName?: string
  taxId?: string
  businessType?: string
  preferredDeliveryMethod?: string
  preferredPaymentMethod?: string
  creditLimit?: number
  paymentTerms?: number
  discountRate?: number
  segment?: string
  loyaltyTier?: string
  loyaltyPoints?: number
  assignedSalesRep?: string
  accountManager?: string
  referredBy?: string
  firstOrderDate?: string
  lastOrderDate?: string
  lastContactDate?: string
  totalOrders?: number
  totalSpent?: number
  averageOrderValue?: number
  notes?: string
  internalNotes?: string
  userId: string
  createdAt: string
  updatedAt: string
}

export interface CreateCustomerData {
  name: string
  customerNumber?: string
  customerType?: string
  status?: string
  email?: string
  phone?: string
  alternatePhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  businessName?: string
  taxId?: string
  businessType?: string
  preferredDeliveryMethod?: string
  preferredPaymentMethod?: string
  creditLimit?: number
  paymentTerms?: number
  discountRate?: number
  segment?: string
  loyaltyTier?: string
  loyaltyPoints?: number
  assignedSalesRep?: string
  accountManager?: string
  referredBy?: string
  notes?: string
  internalNotes?: string
}

export interface UpdateCustomerData extends Partial<CreateCustomerData> {}

class AppwriteCustomerService {
  // Get all customers for current user
  async getCustomers(userId?: string): Promise<Customer[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.CUSTOMERS,
        [
          Query.equal('userId', currentUserId),
          Query.orderAsc('name')
        ]
      )

      return response.documents.map(doc => convertFromAppwriteDocument<Customer>(doc))
    } catch (error: any) {
      throw new Error(`Failed to fetch customers: ${error.message}`)
    }
  }

  // Get customer by ID
  async getCustomerById(id: string, userId?: string): Promise<Customer | null> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.CUSTOMERS,
        id
      )

      const customer = convertFromAppwriteDocument<Customer>(response)
      
      // Verify ownership
      if (customer.userId !== currentUserId) {
        throw new Error('Access denied')
      }

      return customer
    } catch (error: any) {
      if (error.code === 404) {
        return null
      }
      throw new Error(`Failed to fetch customer: ${error.message}`)
    }
  }

  // Create new customer
  async createCustomer(data: CreateCustomerData, userId?: string): Promise<Customer> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Check if customer name already exists for this user
      const existing = await this.getCustomerByName(data.name, currentUserId)
      if (existing) {
        throw new Error('Customer name already exists')
      }

      const documentData = {
        ...convertToAppwriteDocument(data),
        customerType: data.customerType || 'INDIVIDUAL',
        status: data.status || 'ACTIVE',
        country: data.country || 'Philippines',
        creditLimit: data.creditLimit ?? 0.00,
        paymentTerms: data.paymentTerms ?? 30,
        discountRate: data.discountRate ?? 0.00,
        segment: data.segment || 'REGULAR',
        loyaltyTier: data.loyaltyTier || 'BRONZE',
        loyaltyPoints: data.loyaltyPoints ?? 0,
        totalOrders: 0,
        totalSpent: 0.00,
        averageOrderValue: 0.00,
        userId: currentUserId
      }

      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.CUSTOMERS,
        ID.unique(),
        documentData,
        Permission.userOnly(currentUserId)
      )

      return convertFromAppwriteDocument<Customer>(response)
    } catch (error: any) {
      throw new Error(`Failed to create customer: ${error.message}`)
    }
  }

  // Update customer
  async updateCustomer(id: string, data: UpdateCustomerData, userId?: string): Promise<Customer> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Verify ownership
      const existing = await this.getCustomerById(id, currentUserId)
      if (!existing) {
        throw new Error('Customer not found')
      }

      const updateData = convertToAppwriteDocument(data)
      
      // If name is being updated, ensure it's unique
      if (data.name && data.name !== existing.name) {
        const nameCheck = await this.getCustomerByName(data.name, currentUserId)
        if (nameCheck && nameCheck.id !== id) {
          throw new Error('Customer name already exists')
        }
      }

      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.CUSTOMERS,
        id,
        updateData
      )

      return convertFromAppwriteDocument<Customer>(response)
    } catch (error: any) {
      throw new Error(`Failed to update customer: ${error.message}`)
    }
  }

  // Delete customer
  async deleteCustomer(id: string, userId?: string): Promise<void> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Verify ownership
      const existing = await this.getCustomerById(id, currentUserId)
      if (!existing) {
        throw new Error('Customer not found')
      }

      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.CUSTOMERS,
        id
      )
    } catch (error: any) {
      throw new Error(`Failed to delete customer: ${error.message}`)
    }
  }

  // Get customer by name
  async getCustomerByName(name: string, userId?: string): Promise<Customer | null> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.CUSTOMERS,
        [
          Query.equal('userId', currentUserId),
          Query.equal('name', name)
        ]
      )

      if (response.documents.length === 0) {
        return null
      }

      return convertFromAppwriteDocument<Customer>(response.documents[0])
    } catch (error: any) {
      throw new Error(`Failed to fetch customer: ${error.message}`)
    }
  }

  // Search customers
  async searchCustomers(query: string, userId?: string): Promise<Customer[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.CUSTOMERS,
        [
          Query.equal('userId', currentUserId),
          Query.or([
            Query.search('name', query),
            Query.search('email', query),
            Query.search('phone', query)
          ]),
          Query.orderAsc('name')
        ]
      )

      return response.documents.map(doc => convertFromAppwriteDocument<Customer>(doc))
    } catch (error: any) {
      throw new Error(`Failed to search customers: ${error.message}`)
    }
  }

  // Get customers with order counts
  async getCustomersWithCounts(userId?: string): Promise<(Customer & { orderCount: number; toBuyCount: number; toPackCount: number })[]> {
    try {
      const customers = await this.getCustomers(userId)
      
      // TODO: Implement order counting by querying orders collection
      // For now, return with zero counts
      return customers.map(customer => ({
        ...customer,
        orderCount: 0,
        toBuyCount: 0,
        toPackCount: 0
      }))
    } catch (error: any) {
      throw new Error(`Failed to fetch customers with counts: ${error.message}`)
    }
  }
}

// Create and export service instance
export const appwriteCustomerService = new AppwriteCustomerService()
