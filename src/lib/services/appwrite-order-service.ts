import { 
  serverDatabases, 
  databases, 
  DATABASE_ID, 
  COLLECTIONS, 
  convertFromAppwriteDocument,
  convertToAppwriteDocument,
  Query,
  Permission
} from '../appwrite'
import { ID } from 'appwrite'
import { authService } from '../auth'

export interface Order {
  id: string
  productName: string
  quantity?: number
  usageUnit?: string
  comment?: string
  imageFilename?: string
  storePrice?: number
  pasabuyFee?: number
  customerPrice?: number
  isBought?: boolean
  packingStatus?: string
  orderNumber?: string
  priority?: string
  category?: string
  brand?: string
  model?: string
  sku?: string
  barcode?: string
  weight?: number
  dimensions?: string
  color?: string
  size?: string
  material?: string
  condition?: string
  warranty?: string
  source?: string
  sourceUrl?: string
  urgency?: string
  specialInstructions?: string
  estimatedDelivery?: string
  requestedDelivery?: string
  completedAt?: string
  cancelledAt?: string
  cancellationReason?: string
  parentOrderId?: string
  orderGroupId?: string
  deliveryStatus?: string
  deliveryDate?: string
  deliveryMethod?: string
  trackingNumber?: string
  deliveryNotes?: string
  storeCodeId?: string
  customerId?: string
  userId: string
  createdAt: string
  updatedAt: string
}

export interface CreateOrderData {
  productName: string
  quantity?: number
  usageUnit?: string
  comment?: string
  imageFilename?: string
  storePrice?: number
  pasabuyFee?: number
  customerPrice?: number
  isBought?: boolean
  packingStatus?: string
  orderNumber?: string
  priority?: string
  category?: string
  brand?: string
  model?: string
  sku?: string
  barcode?: string
  weight?: number
  dimensions?: string
  color?: string
  size?: string
  material?: string
  condition?: string
  warranty?: string
  source?: string
  sourceUrl?: string
  urgency?: string
  specialInstructions?: string
  estimatedDelivery?: string
  requestedDelivery?: string
  parentOrderId?: string
  orderGroupId?: string
  deliveryStatus?: string
  deliveryMethod?: string
  trackingNumber?: string
  deliveryNotes?: string
  storeCodeId?: string
  customerId?: string
}

export interface UpdateOrderData extends Partial<CreateOrderData> {}

class AppwriteOrderService {
  // Get all orders for current user
  async getOrders(userId?: string): Promise<Order[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        [
          Query.equal('userId', currentUserId),
          Query.orderDesc('$createdAt')
        ]
      )

      return response.documents.map(doc => convertFromAppwriteDocument<Order>(doc))
    } catch (error: any) {
      throw new Error(`Failed to fetch orders: ${error.message}`)
    }
  }

  // Get order by ID
  async getOrderById(id: string, userId?: string): Promise<Order | null> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        id
      )

      const order = convertFromAppwriteDocument<Order>(response)
      
      // Verify ownership
      if (order.userId !== currentUserId) {
        throw new Error('Access denied')
      }

      return order
    } catch (error: any) {
      if (error.code === 404) {
        return null
      }
      throw new Error(`Failed to fetch order: ${error.message}`)
    }
  }

  // Create new order
  async createOrder(data: CreateOrderData, userId?: string): Promise<Order> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const documentData = {
        ...convertToAppwriteDocument(data),
        quantity: data.quantity ?? 1,
        storePrice: data.storePrice ?? 0.00,
        pasabuyFee: data.pasabuyFee ?? 0.00,
        customerPrice: data.customerPrice ?? 0.00,
        isBought: data.isBought ?? false,
        packingStatus: data.packingStatus || 'Not Packed',
        priority: data.priority || 'NORMAL',
        condition: data.condition || 'NEW',
        urgency: data.urgency || 'MEDIUM',
        deliveryStatus: data.deliveryStatus || 'Not Delivered',
        userId: currentUserId
      }

      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        ID.unique(),
        documentData,
        Permission.userOnly(currentUserId)
      )

      return convertFromAppwriteDocument<Order>(response)
    } catch (error: any) {
      throw new Error(`Failed to create order: ${error.message}`)
    }
  }

  // Update order
  async updateOrder(id: string, data: UpdateOrderData, userId?: string): Promise<Order> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Verify ownership
      const existing = await this.getOrderById(id, currentUserId)
      if (!existing) {
        throw new Error('Order not found')
      }

      const updateData = convertToAppwriteDocument(data)

      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        id,
        updateData
      )

      return convertFromAppwriteDocument<Order>(response)
    } catch (error: any) {
      throw new Error(`Failed to update order: ${error.message}`)
    }
  }

  // Delete order
  async deleteOrder(id: string, userId?: string): Promise<void> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Verify ownership
      const existing = await this.getOrderById(id, currentUserId)
      if (!existing) {
        throw new Error('Order not found')
      }

      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        id
      )
    } catch (error: any) {
      throw new Error(`Failed to delete order: ${error.message}`)
    }
  }

  // Get orders by store code
  async getOrdersByStoreCode(storeCodeId: string, userId?: string): Promise<Order[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        [
          Query.equal('userId', currentUserId),
          Query.equal('storeCodeId', storeCodeId),
          Query.orderDesc('$createdAt')
        ]
      )

      return response.documents.map(doc => convertFromAppwriteDocument<Order>(doc))
    } catch (error: any) {
      throw new Error(`Failed to fetch orders by store code: ${error.message}`)
    }
  }

  // Get orders by customer
  async getOrdersByCustomer(customerId: string, userId?: string): Promise<Order[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        [
          Query.equal('userId', currentUserId),
          Query.equal('customerId', customerId),
          Query.orderDesc('$createdAt')
        ]
      )

      return response.documents.map(doc => convertFromAppwriteDocument<Order>(doc))
    } catch (error: any) {
      throw new Error(`Failed to fetch orders by customer: ${error.message}`)
    }
  }

  // Get orders by status
  async getOrdersByStatus(isBought: boolean, packingStatus?: string, userId?: string): Promise<Order[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const queries = [
        Query.equal('userId', currentUserId),
        Query.equal('isBought', isBought),
        Query.orderDesc('$createdAt')
      ]

      if (packingStatus) {
        queries.push(Query.equal('packingStatus', packingStatus))
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        queries
      )

      return response.documents.map(doc => convertFromAppwriteDocument<Order>(doc))
    } catch (error: any) {
      throw new Error(`Failed to fetch orders by status: ${error.message}`)
    }
  }

  // Search orders
  async searchOrders(query: string, userId?: string): Promise<Order[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        [
          Query.equal('userId', currentUserId),
          Query.or([
            Query.search('productName', query),
            Query.search('orderNumber', query)
          ]),
          Query.orderDesc('$createdAt')
        ]
      )

      return response.documents.map(doc => convertFromAppwriteDocument<Order>(doc))
    } catch (error: any) {
      throw new Error(`Failed to search orders: ${error.message}`)
    }
  }

  // Get buy list (orders to buy)
  async getBuyList(userId?: string): Promise<Order[]> {
    return this.getOrdersByStatus(false, undefined, userId)
  }

  // Get packing list (orders to pack)
  async getPackingList(userId?: string): Promise<Order[]> {
    return this.getOrdersByStatus(true, 'Not Packed', userId)
  }

  // Mark order as bought
  async markAsBought(id: string, storePrice: number, pasabuyFee: number, customerPrice: number, userId?: string): Promise<Order> {
    return this.updateOrder(id, {
      isBought: true,
      storePrice,
      pasabuyFee,
      customerPrice
    }, userId)
  }

  // Mark order as packed
  async markAsPacked(id: string, userId?: string): Promise<Order> {
    return this.updateOrder(id, {
      packingStatus: 'Packed'
    }, userId)
  }

  // Get order statistics
  async getOrderStats(userId?: string): Promise<{
    total: number
    toBuy: number
    toPack: number
    packed: number
    totalValue: number
  }> {
    try {
      const orders = await this.getOrders(userId)
      
      const stats = {
        total: orders.length,
        toBuy: orders.filter(o => !o.isBought).length,
        toPack: orders.filter(o => o.isBought && o.packingStatus === 'Not Packed').length,
        packed: orders.filter(o => o.packingStatus === 'Packed').length,
        totalValue: orders.reduce((sum, o) => sum + (o.customerPrice || 0), 0)
      }

      return stats
    } catch (error: any) {
      throw new Error(`Failed to get order statistics: ${error.message}`)
    }
  }
}

// Create and export service instance
export const appwriteOrderService = new AppwriteOrderService()
