import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Handles number input changes properly, allowing users to clear the field
 * without immediately converting empty strings to default values.
 *
 * @param value - The input value from the change event
 * @param options - Configuration options
 * @returns The processed value (string for empty, number for valid numbers)
 */
export function handleNumberInputChange(
  value: string,
  options: {
    allowEmpty?: boolean
    defaultValue?: number
    min?: number
    max?: number
    integer?: boolean
  } = {}
) {
  const { allowEmpty = true, defaultValue = 0, min, max, integer = false } = options

  // Allow empty string if allowEmpty is true
  if (value === '' && allowEmpty) {
    return ''
  }

  // Parse the number
  const parsed = integer ? parseInt(value, 10) : parseFloat(value)

  // If parsing failed, return empty string or default
  if (isNaN(parsed)) {
    return allowEmpty ? '' : defaultValue
  }

  // Apply min/max constraints if specified
  let result = parsed
  if (typeof min === 'number' && result < min) {
    result = min
  }
  if (typeof max === 'number' && result > max) {
    result = max
  }

  return result
}

/**
 * Converts a form value to a number for submission, handling empty strings
 *
 * @param value - The form value (could be string or number)
 * @param defaultValue - Default value to use for empty strings
 * @param integer - Whether to parse as integer
 * @returns The numeric value
 */
export function convertToNumber(
  value: string | number | undefined,
  defaultValue: number = 0,
  integer: boolean = false
): number {
  if (typeof value === 'number') {
    return value
  }

  if (typeof value === 'string' && value.trim() !== '') {
    const parsed = integer ? parseInt(value, 10) : parseFloat(value)
    return isNaN(parsed) ? defaultValue : parsed
  }

  return defaultValue
}
