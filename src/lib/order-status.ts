// Order status utilities for PasaBuy Pal

export type OrderStatus = 'pending' | 'bought' | 'packed' | 'delivered'

export interface Order {
  id: number
  isBought: boolean
  packingStatus: string
}

// Status determination logic
export function getOrderStatus(order: Order): OrderStatus {
  if (!order.isBought) return 'pending'
  if (order.packingStatus === 'Packed') return 'packed'
  if (order.isBought && order.packingStatus !== 'Packed') return 'bought'
  return 'pending'
}

// Status display configuration with dark mode support
export const statusConfig = {
  pending: {
    label: 'Pending',
    shortLabel: 'To Buy',
    description: 'Not yet purchased',
    color: 'bg-muted/60 text-muted-foreground border-border dark:bg-muted/40 dark:text-muted-foreground',
    dotColor: 'bg-muted-foreground',
    borderColor: 'border-l-muted-foreground/60',
    bgColor: 'bg-muted/20 dark:bg-muted/10',
    icon: 'LuShoppingCart'
  },
  bought: {
    label: 'Bought',
    shortLabel: 'Bought',
    description: 'Purchased, ready to pack',
    color: 'bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-800/50',
    dotColor: 'bg-emerald-500 dark:bg-emerald-400',
    borderColor: 'border-l-emerald-500 dark:border-l-emerald-400',
    bgColor: 'bg-emerald-50/50 dark:bg-emerald-900/10',
    icon: 'LuPackage'
  },
  packed: {
    label: 'Packed',
    shortLabel: 'Packed',
    description: 'Packed and ready for delivery',
    color: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800/50',
    dotColor: 'bg-blue-500 dark:bg-blue-400',
    borderColor: 'border-l-blue-500 dark:border-l-blue-400',
    bgColor: 'bg-blue-50/50 dark:bg-blue-900/10',
    icon: 'LuBox'
  },
  delivered: {
    label: 'Delivered',
    shortLabel: 'Delivered',
    description: 'Completed and delivered',
    color: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800/50',
    dotColor: 'bg-purple-500 dark:bg-purple-400',
    borderColor: 'border-l-purple-500 dark:border-l-purple-400',
    bgColor: 'bg-purple-50/50 dark:bg-purple-900/10',
    icon: 'LuTruck'
  }
} as const

// Get status configuration
export function getStatusConfig(status: OrderStatus) {
  return statusConfig[status]
}

// Get status badge classes
export function getStatusBadgeClasses(order: Order): string {
  const status = getOrderStatus(order)
  return statusConfig[status].color
}

// Get status border classes for cards
export function getStatusBorderClasses(order: Order): string {
  const status = getOrderStatus(order)
  return `border-l-4 ${statusConfig[status].borderColor}`
}

// Get status background classes for cards
export function getStatusBackgroundClasses(order: Order): string {
  const status = getOrderStatus(order)
  return statusConfig[status].bgColor
}

// Get status dot classes
export function getStatusDotClasses(order: Order): string {
  const status = getOrderStatus(order)
  return `w-2 h-2 rounded-full ${statusConfig[status].dotColor}`
}

// Get status label
export function getStatusLabel(order: Order, short: boolean = false): string {
  const status = getOrderStatus(order)
  return short ? statusConfig[status].shortLabel : statusConfig[status].label
}

// Get status description
export function getStatusDescription(order: Order): string {
  const status = getOrderStatus(order)
  return statusConfig[status].description
}

// Check if status allows certain actions
export function canMarkAsBought(order: Order): boolean {
  return !order.isBought
}

export function canMarkAsPacked(order: Order): boolean {
  return order.isBought && order.packingStatus !== 'Packed'
}

export function canMarkAsDelivered(order: Order): boolean {
  return order.isBought && order.packingStatus === 'Packed'
}

// Status priority for sorting (lower number = higher priority)
export function getStatusPriority(order: Order): number {
  const status = getOrderStatus(order)
  const priorities = {
    pending: 1,
    bought: 2,
    packed: 3,
    delivered: 4
  }
  return priorities[status]
}

// Get next possible status
export function getNextStatus(order: Order): OrderStatus | null {
  const currentStatus = getOrderStatus(order)

  switch (currentStatus) {
    case 'pending':
      return 'bought'
    case 'bought':
      return 'packed'
    case 'packed':
      return 'delivered'
    case 'delivered':
      return null // No next status
    default:
      return null
  }
}

// Get status transition action label
export function getStatusActionLabel(order: Order): string | null {
  const nextStatus = getNextStatus(order)
  if (!nextStatus) return null

  const actionLabels: Record<string, string> = {
    bought: 'Mark as Bought',
    packed: 'Mark as Packed',
    delivered: 'Mark as Delivered'
  }

  return actionLabels[nextStatus] || null
}
