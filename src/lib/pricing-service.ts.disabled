import { prisma } from '@/lib/db'

export interface PricingTier {
  id?: number
  minPrice: number
  maxPrice: number | null
  markupType: 'PERCENTAGE' | 'FIXED_AMOUNT'
  markupValue: number
  pasabuyFee?: number
  sortOrder: number
}

export interface PricingCalculationResult {
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  breakdown: {
    storePrice: number
    markup: number
    serviceFee: number
    total: number
  }
  appliedTier: {
    minPrice: number
    maxPrice: number | null
    markupType: string
    markupValue: number
  } | null
  serviceFee: number
}

export interface PricingSettingsData {
  markupType: 'PERCENTAGE' | 'FIXED_AMOUNT'
  markupValue: number
  serviceFee: number
}

export interface StorePricingData {
  storeCodeId: number
  name: string
  serviceFee: number
  pricingTiers: PricingTier[]
}

export interface DefaultPricingData {
  serviceFee: number
  pricingTiers: PricingTier[]
}

export class PricingService {
  /**
   * Calculate customer price based on store price and store-specific or default pricing settings
   */
  static async calculateCustomerPrice(storePrice: number, storeCodeId?: number): Promise<PricingCalculationResult> {
    if (storePrice <= 0) {
      return {
        storePrice: 0,
        pasabuyFee: 0,
        customerPrice: 0,
        breakdown: {
          storePrice: 0,
          markup: 0,
          serviceFee: 0,
          total: 0
        },
        appliedTier: null,
        serviceFee: 0
      }
    }

    // Get store-specific pricing or default pricing
    const pricingConfig = await this.getPricingForStore(storeCodeId)

    // Find the appropriate tier for this store price
    const applicableTier = this.findApplicableTier(storePrice, pricingConfig.pricingTiers)

    if (!applicableTier) {
      throw new Error(`No pricing tier found for store price ₱${storePrice}`)
    }

    // Calculate markup based on the applicable tier
    const markup = this.calculateTierMarkup(storePrice, applicableTier)
    const pasabuyFee = applicableTier.pasabuyFee || pricingConfig.serviceFee // Use tier-specific Pasabuy fee or fallback
    const customerPrice = storePrice + markup - pasabuyFee

    return {
      storePrice,
      pasabuyFee, // This is now only the service fee portion
      customerPrice,
      breakdown: {
        storePrice,
        markup,
        serviceFee: -pasabuyFee, // Negative because it's subtracted
        total: customerPrice
      },
      appliedTier: {
        minPrice: applicableTier.minPrice,
        maxPrice: applicableTier.maxPrice,
        markupType: applicableTier.markupType,
        markupValue: applicableTier.markupValue
      },
      serviceFee: pasabuyFee
    }
  }

  /**
   * Get pricing for a specific store or default pricing
   */
  static async getPricingForStore(storeCodeId?: number) {
    if (storeCodeId) {
      // Try to get store-specific pricing with tiers
      const storePricing = await prisma.storePricing.findUnique({
        where: {
          storeCodeId: storeCodeId,
          isActive: true
        },
        include: {
          pricingTiers: {
            orderBy: { sortOrder: 'asc' }
          }
        }
      })

      if (storePricing) {
        return storePricing
      }
    }

    // Fall back to default pricing
    return await this.getDefaultPricing()
  }

  /**
   * Get default pricing settings (creates default if none exist)
   */
  static async getDefaultPricing() {
    let settings = await prisma.defaultPricing.findFirst({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' },
      include: {
        pricingTiers: {
          orderBy: { sortOrder: 'asc' }
        }
      }
    })

    // Create default settings if none exist
    if (!settings) {
      settings = await prisma.defaultPricing.create({
        data: {
          serviceFee: 20.00, // ₱20 service fee
          isActive: true,
          pricingTiers: {
            create: [
              {
                minPrice: 0,
                maxPrice: null, // "and above" - covers all prices
                markupType: 'PERCENTAGE',
                markupValue: 100.00, // 100% markup (double the price)
                pasabuyFee: 20.00, // ₱20 Pasabuy fee
                sortOrder: 0
              }
            ]
          }
        },
        include: {
          pricingTiers: {
            orderBy: { sortOrder: 'asc' }
          }
        }
      })
    }

    return settings
  }

  /**
   * Find the applicable pricing tier for a given store price
   */
  private static findApplicableTier(storePrice: number, tiers: any[]): any | null {
    // Sort tiers by sortOrder to ensure proper precedence
    const sortedTiers = tiers.sort((a, b) => a.sortOrder - b.sortOrder)

    for (const tier of sortedTiers) {
      const minPrice = tier.minPrice || 0
      const maxPrice = tier.maxPrice

      // Check if store price falls within this tier's range
      if (storePrice >= minPrice) {
        if (maxPrice === null || storePrice <= maxPrice) {
          return tier
        }
      }
    }

    return null
  }

  /**
   * Calculate markup based on a specific pricing tier
   */
  private static calculateTierMarkup(storePrice: number, tier: any): number {
    switch (tier.markupType) {
      case 'PERCENTAGE':
        return storePrice * (tier.markupValue / 100)
      case 'FIXED_AMOUNT':
        return tier.markupValue
      default:
        return 0
    }
  }

  /**
   * Validate that pricing tiers don't overlap and cover all price points
   */
  static validatePricingTiers(tiers: PricingTier[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (tiers.length === 0) {
      errors.push('At least one pricing tier is required')
      return { isValid: false, errors }
    }

    // Sort tiers by minPrice for validation
    const sortedTiers = [...tiers].sort((a, b) => a.minPrice - b.minPrice)

    // Check for gaps and overlaps
    for (let i = 0; i < sortedTiers.length; i++) {
      const currentTier = sortedTiers[i]
      const nextTier = sortedTiers[i + 1]

      // Validate current tier
      if (currentTier.minPrice < 0) {
        errors.push(`Tier ${i + 1}: Minimum price cannot be negative`)
      }

      if (currentTier.maxPrice !== null && currentTier.maxPrice <= currentTier.minPrice) {
        errors.push(`Tier ${i + 1}: Maximum price must be greater than minimum price`)
      }

      if (currentTier.markupValue < 0) {
        errors.push(`Tier ${i + 1}: Markup value cannot be negative`)
      }

      // Check for gaps between tiers
      if (nextTier && currentTier.maxPrice !== null) {
        if (currentTier.maxPrice + 0.01 < nextTier.minPrice) {
          errors.push(`Gap between tier ${i + 1} (max: ₱${currentTier.maxPrice}) and tier ${i + 2} (min: ₱${nextTier.minPrice})`)
        }

        // Check for overlaps
        if (currentTier.maxPrice >= nextTier.minPrice) {
          errors.push(`Overlap between tier ${i + 1} and tier ${i + 2}`)
        }
      }
    }

    // Ensure the last tier covers "and above" or there's no gap at the end
    const lastTier = sortedTiers[sortedTiers.length - 1]
    if (lastTier.maxPrice !== null) {
      errors.push('The highest tier must have no maximum price (to cover all prices above)')
    }

    // Ensure the first tier starts at 0
    if (sortedTiers[0].minPrice > 0) {
      errors.push('The first tier must start at ₱0 to cover all possible prices')
    }

    return { isValid: errors.length === 0, errors }
  }

  /**
   * Update default pricing settings
   */
  static async updateDefaultPricing(data: DefaultPricingData) {
    // Validate pricing tiers
    const validation = this.validatePricingTiers(data.pricingTiers)
    if (!validation.isValid) {
      throw new Error(`Invalid pricing tiers: ${validation.errors.join(', ')}`)
    }

    // Deactivate current settings
    await prisma.defaultPricing.updateMany({
      where: { isActive: true },
      data: { isActive: false }
    })

    // Create new settings with tiers
    return await prisma.defaultPricing.create({
      data: {
        serviceFee: data.serviceFee,
        isActive: true,
        pricingTiers: {
          create: data.pricingTiers.map((tier, index) => ({
            minPrice: tier.minPrice,
            maxPrice: tier.maxPrice,
            markupType: tier.markupType,
            markupValue: tier.markupValue,
            pasabuyFee: tier.pasabuyFee || 0,
            sortOrder: tier.sortOrder || index
          }))
        }
      },
      include: {
        pricingTiers: {
          orderBy: { sortOrder: 'asc' }
        }
      }
    })
  }

  /**
   * Get all store pricing configurations
   */
  static async getAllStorePricing() {
    return await prisma.storePricing.findMany({
      include: {
        storeCode: {
          select: {
            id: true,
            code: true,
            name: true
          }
        },
        pricingTiers: {
          orderBy: { sortOrder: 'asc' }
        }
      },
      orderBy: [
        { isActive: 'desc' },
        { storeCode: { code: 'asc' } }
      ]
    })
  }

  /**
   * Create store-specific pricing
   */
  static async createStorePricing(data: StorePricingData) {
    // Validate pricing tiers
    const validation = this.validatePricingTiers(data.pricingTiers)
    if (!validation.isValid) {
      throw new Error(`Invalid pricing tiers: ${validation.errors.join(', ')}`)
    }

    return await prisma.storePricing.create({
      data: {
        storeCodeId: data.storeCodeId,
        name: data.name,
        serviceFee: data.serviceFee,
        isActive: true,
        pricingTiers: {
          create: data.pricingTiers.map((tier, index) => ({
            minPrice: tier.minPrice,
            maxPrice: tier.maxPrice,
            markupType: tier.markupType,
            markupValue: tier.markupValue,
            pasabuyFee: tier.pasabuyFee || 0,
            sortOrder: tier.sortOrder || index
          }))
        }
      },
      include: {
        storeCode: {
          select: {
            id: true,
            code: true,
            name: true
          }
        },
        pricingTiers: {
          orderBy: { sortOrder: 'asc' }
        }
      }
    })
  }

  /**
   * Update store-specific pricing
   */
  static async updateStorePricing(storeCodeId: number, data: Partial<StorePricingData>) {
    // If pricing tiers are provided, validate them
    if (data.pricingTiers) {
      const validation = this.validatePricingTiers(data.pricingTiers)
      if (!validation.isValid) {
        throw new Error(`Invalid pricing tiers: ${validation.errors.join(', ')}`)
      }
    }

    // Get the current pricing configuration
    const currentPricing = await prisma.storePricing.findUnique({
      where: { storeCodeId },
      include: { pricingTiers: true }
    })

    if (!currentPricing) {
      throw new Error('Store pricing configuration not found')
    }

    // Update the pricing configuration
    return await prisma.storePricing.update({
      where: { storeCodeId },
      data: {
        name: data.name,
        serviceFee: data.serviceFee,
        updatedAt: new Date(),
        ...(data.pricingTiers && {
          pricingTiers: {
            deleteMany: {}, // Delete existing tiers
            create: data.pricingTiers.map((tier, index) => ({
              minPrice: tier.minPrice,
              maxPrice: tier.maxPrice,
              markupType: tier.markupType,
              markupValue: tier.markupValue,
              pasabuyFee: tier.pasabuyFee || 0,
              sortOrder: tier.sortOrder || index
            }))
          }
        })
      },
      include: {
        storeCode: {
          select: {
            id: true,
            code: true,
            name: true
          }
        },
        pricingTiers: {
          orderBy: { sortOrder: 'asc' }
        }
      }
    })
  }

  /**
   * Delete store-specific pricing
   */
  static async deleteStorePricing(storeCodeId: number) {
    return await prisma.storePricing.delete({
      where: { storeCodeId }
    })
  }

  /**
   * Migration helper: Convert old single-tier pricing to new tiered structure
   */
  static async migrateOldPricingToTiers() {
    console.log('Starting migration of old pricing configurations to tiered structure...')

    try {
      // This would be used if there were old pricing configurations to migrate
      // For now, we'll ensure all existing configurations have proper tier structure

      const storePricings = await prisma.storePricing.findMany({
        include: { pricingTiers: true }
      })

      for (const pricing of storePricings) {
        if (pricing.pricingTiers.length === 0) {
          console.log(`Migrating store pricing ${pricing.id} to tiered structure`)
          // This would convert old markupType/markupValue to a single tier
          // Since we've already updated the schema, this is mainly for reference
        }
      }

      console.log('Migration completed successfully')
    } catch (error) {
      console.error('Migration failed:', error)
      throw error
    }
  }

  /**
   * Get pricing calculation examples for different store prices
   */
  static async getPricingExamples(storeCodeId?: number, examplePrices: number[] = [100, 300, 500, 1000]) {
    const examples = []

    for (const price of examplePrices) {
      try {
        const result = await this.calculateCustomerPrice(price, storeCodeId)
        examples.push({
          storePrice: price,
          customerPrice: result.customerPrice,
          markup: result.breakdown.markup,
          serviceFee: result.serviceFee,
          appliedTier: result.appliedTier
        })
      } catch (error) {
        examples.push({
          storePrice: price,
          error: error instanceof Error ? error.message : 'Calculation failed'
        })
      }
    }

    return examples
  }
}
