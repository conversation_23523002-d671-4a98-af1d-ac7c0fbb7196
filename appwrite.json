{"projectId": "pasabuy-pal", "projectName": "PasaB<PERSON>", "functions": [], "databases": [{"databaseId": "pasa<PERSON>y_pal_db", "name": "PasaBuy Pal Database", "collections": []}], "storage": [{"bucketId": "product-images", "name": "Product Images", "permissions": ["read(\"any\")"], "fileSecurity": true, "enabled": true, "maximumFileSize": 30000000, "allowedFileExtensions": ["jpg", "jpeg", "png", "gif", "webp", "svg"], "compression": "gzip", "encryption": true, "antivirus": true}]}