# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

/src/generated/prisma

# Database
*.db
*.db-journal

# Uploads
/public/uploads/orders/*
!/public/uploads/orders/.gitkeep

# Prisma
/prisma/migrations/

# Test files and documentation (prevent deployment)
test-*.html
test-*.md
*.test.html
demo-*.html
*-test.html

# Backup files
backup-*/
*.backup
*.bak
*.tar.gz
pasabuy-pal-backup-*.tar.gz

# Export files
exports/
*.json.export
*-export-*.json

# PWA files (generated)
public/sw.js
public/workbox-*.js

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp

# Cleanup and migration logs
cleanup-log.md
removal-log.md
migration-*.json
*-migration.json

# Documentation that shouldn't be deployed
docs/internal/
*.internal.md

# Appwrite backup files
appwrite.json.backup

# OS generated files
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Additional log files
logs/
*.log

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Turbo
.turbo/

# Sentry
.sentryclirc
