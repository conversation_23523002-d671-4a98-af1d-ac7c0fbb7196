#!/bin/bash

# PasaBuy Pal Quick Deployment Script
# This script helps you quickly deploy the PasaBuy Pal release package

set -e

RELEASE_FILE="pasabuy-pal-v1.0.0-production-20250603_134226.tar.gz"
APP_DIR="pasabuy-pal-production"

echo "🚀 PasaBuy Pal Quick Deployment"
echo "================================"

# Check if release file exists
if [ ! -f "$RELEASE_FILE" ]; then
    echo "❌ Error: Release file $RELEASE_FILE not found!"
    echo "Please ensure the release archive is in the current directory."
    exit 1
fi

echo "📦 Found release file: $RELEASE_FILE"

# Extract the archive
echo "📂 Extracting release package..."
tar -xzf "$RELEASE_FILE"

# Get the extracted directory name (it includes timestamp)
EXTRACTED_DIR=$(tar -tzf "$RELEASE_FILE" | head -1 | cut -f1 -d"/")

# Rename to a cleaner directory name
if [ "$EXTRACTED_DIR" != "$APP_DIR" ]; then
    mv "$EXTRACTED_DIR" "$APP_DIR"
fi

echo "✅ Extracted to: $APP_DIR"

# Enter the application directory
cd "$APP_DIR"

echo "🔧 Setting up environment..."

# Check if .env.production exists and copy it
if [ -f ".env.production" ]; then
    cp .env.production .env.local
    echo "📝 Created .env.local from template"
    echo "⚠️  IMPORTANT: Edit .env.local with your production values!"
else
    echo "⚠️  Warning: .env.production template not found"
fi

echo "📦 Installing dependencies..."
npm install --production

echo ""
echo "✅ Deployment setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Edit .env.local with your production configuration:"
echo "   - NEXT_PUBLIC_APP_URL"
echo "   - NEXT_PUBLIC_APPWRITE_PROJECT_ID"
echo "   - APPWRITE_API_KEY"
echo "   - APPWRITE_DATABASE_ID"
echo "   - APPWRITE_STORAGE_BUCKET_ID"
echo ""
echo "2. Set up your Appwrite backend:"
echo "   npm run appwrite:setup"
echo ""
echo "3. Start the application:"
echo "   npm start"
echo ""
echo "4. Access your application at: http://localhost:3000"
echo ""
echo "📚 For detailed instructions, see:"
echo "   - RELEASE.md"
echo "   - DEPLOYMENT_CHECKLIST.md"
echo "   - APPWRITE_DEPLOYMENT.md"
echo ""
echo "🎉 Happy deploying!"
