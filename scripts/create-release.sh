#!/bin/bash

# PasaBuy Pal Release Creator
# Creates a production-ready tar.gz release package

set -e

# Configuration
APP_NAME="pasabuy-pal"
VERSION="1.0.0"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RELEASE_NAME="${APP_NAME}-v${VERSION}-${TIMESTAMP}"
RELEASE_DIR="releases"
TEMP_DIR="temp_release"

echo "🚀 Creating PasaBuy Pal Release Package..."
echo "📦 Release: ${RELEASE_NAME}"

# Create releases directory if it doesn't exist
mkdir -p ${RELEASE_DIR}

# Clean up any existing temp directory
rm -rf ${TEMP_DIR}

# Create temporary directory for release
mkdir -p ${TEMP_DIR}/${RELEASE_NAME}

echo "📋 Building application..."
# Ensure we have a fresh build
npm run build

echo "📂 Copying production files..."

# Copy essential application files
cp -r .next ${TEMP_DIR}/${RELEASE_NAME}/
cp -r public ${TEMP_DIR}/${RELEASE_NAME}/
cp -r src ${TEMP_DIR}/${RELEASE_NAME}/
cp -r prisma ${TEMP_DIR}/${RELEASE_NAME}/

# Copy configuration files
cp package.json ${TEMP_DIR}/${RELEASE_NAME}/
cp package-lock.json ${TEMP_DIR}/${RELEASE_NAME}/
cp next.config.mjs ${TEMP_DIR}/${RELEASE_NAME}/
cp tsconfig.json ${TEMP_DIR}/${RELEASE_NAME}/
cp tailwind.config.ts ${TEMP_DIR}/${RELEASE_NAME}/
cp postcss.config.mjs ${TEMP_DIR}/${RELEASE_NAME}/
cp components.json ${TEMP_DIR}/${RELEASE_NAME}/

# Copy environment template
cp .env.production ${TEMP_DIR}/${RELEASE_NAME}/

# Copy documentation
cp README.md ${TEMP_DIR}/${RELEASE_NAME}/
cp RELEASE.md ${TEMP_DIR}/${RELEASE_NAME}/
cp DEPLOYMENT_CHECKLIST.md ${TEMP_DIR}/${RELEASE_NAME}/
cp APPWRITE_DEPLOYMENT.md ${TEMP_DIR}/${RELEASE_NAME}/

# Copy scripts (excluding this one)
mkdir -p ${TEMP_DIR}/${RELEASE_NAME}/scripts
cp scripts/setup-appwrite-database.ts ${TEMP_DIR}/${RELEASE_NAME}/scripts/
cp scripts/import-to-appwrite.ts ${TEMP_DIR}/${RELEASE_NAME}/scripts/
cp scripts/complete-migration.ts ${TEMP_DIR}/${RELEASE_NAME}/scripts/
cp scripts/create-pwa-icons.js ${TEMP_DIR}/${RELEASE_NAME}/scripts/

echo "📝 Creating release info..."
# Create release info file
cat > ${TEMP_DIR}/${RELEASE_NAME}/RELEASE_INFO.txt << EOF
PasaBuy Pal Release Package
===========================

Version: ${VERSION}
Build Date: $(date)
Git Branch: $(git branch --show-current)
Git Commit: $(git rev-parse HEAD)

Package Contents:
- Built Next.js application (.next/)
- Source code (src/)
- Database schema (prisma/)
- Static assets (public/)
- Configuration files
- Documentation
- Setup scripts

Deployment Instructions:
1. Extract this archive to your server
2. Copy .env.production to .env.local and configure
3. Run: npm install --production
4. Run: npm start
5. Follow DEPLOYMENT_CHECKLIST.md

For detailed instructions, see RELEASE.md
EOF

echo "🗜️ Creating compressed archive..."
# Create the tar.gz archive
cd ${TEMP_DIR}
tar -czf ../${RELEASE_DIR}/${RELEASE_NAME}.tar.gz ${RELEASE_NAME}/
cd ..

# Clean up temp directory
rm -rf ${TEMP_DIR}

# Get file size
FILE_SIZE=$(du -h ${RELEASE_DIR}/${RELEASE_NAME}.tar.gz | cut -f1)

echo "✅ Release package created successfully!"
echo "📦 File: ${RELEASE_DIR}/${RELEASE_NAME}.tar.gz"
echo "📏 Size: ${FILE_SIZE}"
echo ""
echo "🚀 Ready for deployment!"
echo "📋 Next steps:"
echo "   1. Upload ${RELEASE_NAME}.tar.gz to your server"
echo "   2. Extract: tar -xzf ${RELEASE_NAME}.tar.gz"
echo "   3. Follow DEPLOYMENT_CHECKLIST.md"
