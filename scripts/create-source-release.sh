#!/bin/bash

# PasaBuy Pal Source Code Release Creator
# Creates a lightweight source-code-only release package

set -e

# Configuration
APP_NAME="pasabuy-pal"
VERSION="1.0.0"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RELEASE_NAME="${APP_NAME}-v${VERSION}-source-${TIMESTAMP}"
RELEASE_DIR="releases"

echo "📦 Creating PasaBuy Pal Source Code Release..."
echo "🏷️  Release: ${RELEASE_NAME}"

# Create releases directory if it doesn't exist
mkdir -p ${RELEASE_DIR}

echo "🗜️ Creating source code archive..."

# Create source-only tar.gz (excluding build artifacts and large files)
tar --exclude='node_modules' \
    --exclude='.git' \
    --exclude='backup-*' \
    --exclude='exports' \
    --exclude='*.tar.gz' \
    --exclude='temp_release' \
    --exclude='releases' \
    --exclude='.next' \
    --exclude='out' \
    --exclude='build' \
    --exclude='dist' \
    --exclude='public/uploads' \
    --exclude='uploads' \
    --exclude='prisma/dev.db' \
    --exclude='*.db' \
    --exclude='*.sqlite' \
    --exclude='*.tsbuildinfo' \
    --exclude='.env' \
    --exclude='.env.local' \
    --exclude='.env.development.local' \
    --exclude='.env.test.local' \
    --exclude='.env.production.local' \
    --exclude='*.log' \
    --exclude='.DS_Store' \
    --exclude='Thumbs.db' \
    --exclude='.vscode' \
    --exclude='.idea' \
    -czf ${RELEASE_DIR}/${RELEASE_NAME}.tar.gz .

# Get file size
FILE_SIZE=$(du -h ${RELEASE_DIR}/${RELEASE_NAME}.tar.gz | cut -f1)

echo "✅ Source code release created successfully!"
echo "📦 File: ${RELEASE_DIR}/${RELEASE_NAME}.tar.gz"
echo "📏 Size: ${FILE_SIZE}"
echo ""
echo "📋 Package contains:"
echo "   ✅ Source code (src/)"
echo "   ✅ Database schema (prisma/)"
echo "   ✅ Configuration files"
echo "   ✅ Documentation"
echo "   ✅ Scripts and setup files"
echo "   ✅ Static assets (public/)"
echo ""
echo "📋 Excluded from package:"
echo "   ❌ node_modules/ (install with npm install)"
echo "   ❌ .next/ (build with npm run build)"
echo "   ❌ Database files (*.db)"
echo "   ❌ Environment files (.env*)"
echo "   ❌ Build artifacts"
echo "   ❌ User uploads"
echo ""
echo "🚀 Ready for distribution!"
echo "📋 To deploy:"
echo "   1. Extract: tar -xzf ${RELEASE_NAME}.tar.gz"
echo "   2. Install: npm install"
echo "   3. Configure: cp .env.production .env.local (and edit)"
echo "   4. Build: npm run build"
echo "   5. Start: npm start"
