#!/usr/bin/env tsx

/**
 * Test script to verify the current migration status
 * This script tests basic functionality without requiring server-side API access
 */

import { existsSync } from 'fs'
import { join } from 'path'

function testMigrationStatus() {
  console.log('🔍 Testing Appwrite Migration Status...\n')
  
  const results = {
    passed: 0,
    failed: 0,
    warnings: 0
  }

  // Test 1: Environment Configuration
  console.log('📋 Test 1: Environment Configuration')
  try {
    const envPath = join(process.cwd(), '.env')
    if (!existsSync(envPath)) {
      console.log('❌ .env file not found')
      results.failed++
    } else {
      const envContent = require('fs').readFileSync(envPath, 'utf8')
      
      // Check for Appwrite variables
      const hasAppwriteEndpoint = envContent.includes('NEXT_PUBLIC_APPWRITE_ENDPOINT')
      const hasAppwriteProject = envContent.includes('NEXT_PUBLIC_APPWRITE_PROJECT_ID')
      const hasAppwriteKey = envContent.includes('APPWRITE_API_KEY')
      const hasAppwriteDatabase = envContent.includes('APPWRITE_DATABASE_ID')
      const hasAppwriteStorage = envContent.includes('APPWRITE_STORAGE_BUCKET_ID')
      
      // Check if DATABASE_URL is commented out or removed
      const hasDatabaseUrl = envContent.includes('DATABASE_URL=') && !envContent.includes('# DATABASE_URL=')
      
      if (hasAppwriteEndpoint && hasAppwriteProject && hasAppwriteKey && hasAppwriteDatabase && hasAppwriteStorage) {
        console.log('✅ All Appwrite environment variables present')
        results.passed++
      } else {
        console.log('❌ Missing Appwrite environment variables')
        results.failed++
      }
      
      if (!hasDatabaseUrl) {
        console.log('✅ DATABASE_URL properly removed/commented')
        results.passed++
      } else {
        console.log('⚠️ DATABASE_URL still active (legacy fallback)')
        results.warnings++
      }
    }
  } catch (error) {
    console.log('❌ Error checking environment:', error)
    results.failed++
  }

  // Test 2: Legacy API Routes Disabled
  console.log('\n📋 Test 2: Legacy API Routes')
  const legacyRoutes = [
    'src/app/api/upload/route.ts',
    'src/app/api/orders/route.ts',
    'src/app/api/customers/route.ts',
    'src/app/api/store-codes/route.ts',
    'src/app/api/invoices/route.ts'
  ]
  
  const disabledRoutes = [
    'src/app/api/upload/route.ts.disabled',
    'src/app/api/orders/route.ts.disabled',
    'src/app/api/customers/route.ts.disabled',
    'src/app/api/store-codes/route.ts.disabled',
    'src/app/api/invoices/route.ts.disabled'
  ]

  let routesDisabled = 0
  legacyRoutes.forEach((route, index) => {
    const legacyExists = existsSync(join(process.cwd(), route))
    const disabledExists = existsSync(join(process.cwd(), disabledRoutes[index]))
    
    if (!legacyExists && disabledExists) {
      routesDisabled++
    }
  })

  if (routesDisabled === legacyRoutes.length) {
    console.log('✅ All legacy API routes properly disabled')
    results.passed++
  } else {
    console.log(`⚠️ ${routesDisabled}/${legacyRoutes.length} legacy routes disabled`)
    results.warnings++
  }

  // Test 3: Appwrite API Routes Present
  console.log('\n📋 Test 3: Appwrite API Routes')
  const appwriteRoutes = [
    'src/app/api/appwrite/upload/route.ts',
    'src/app/api/appwrite/orders/route.ts',
    'src/app/api/appwrite/customers/route.ts',
    'src/app/api/appwrite/store-codes/route.ts',
    'src/app/api/appwrite/invoices/route.ts'
  ]

  let appwriteRoutesPresent = 0
  appwriteRoutes.forEach(route => {
    if (existsSync(join(process.cwd(), route))) {
      appwriteRoutesPresent++
    }
  })

  if (appwriteRoutesPresent === appwriteRoutes.length) {
    console.log('✅ All Appwrite API routes present')
    results.passed++
  } else {
    console.log(`⚠️ ${appwriteRoutesPresent}/${appwriteRoutes.length} Appwrite routes present`)
    results.warnings++
  }

  // Test 4: Prisma Dependencies Removed
  console.log('\n📋 Test 4: Prisma Dependencies')
  try {
    const packageJsonPath = join(process.cwd(), 'package.json')
    if (existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(require('fs').readFileSync(packageJsonPath, 'utf8'))
      
      const hasPrismaInDeps = packageJson.dependencies?.prisma
      const hasPrismaInDevDeps = packageJson.devDependencies?.prisma
      const hasPrismaClient = packageJson.dependencies?.['@prisma/client']
      
      if (!hasPrismaInDeps && !hasPrismaInDevDeps && !hasPrismaClient) {
        console.log('✅ Prisma dependencies removed from package.json')
        results.passed++
      } else {
        console.log('⚠️ Some Prisma dependencies still present')
        results.warnings++
      }
    }
  } catch (error) {
    console.log('❌ Error checking package.json:', error)
    results.failed++
  }

  // Test 5: Prisma Schema Disabled
  console.log('\n📋 Test 5: Prisma Schema')
  const prismaSchemaExists = existsSync(join(process.cwd(), 'prisma/schema.prisma'))
  const prismaDisabledExists = existsSync(join(process.cwd(), 'prisma.disabled'))
  
  if (!prismaSchemaExists && prismaDisabledExists) {
    console.log('✅ Prisma schema properly disabled')
    results.passed++
  } else if (prismaSchemaExists) {
    console.log('⚠️ Prisma schema still active (legacy fallback)')
    results.warnings++
  } else {
    console.log('❌ Prisma schema missing (may cause issues)')
    results.failed++
  }

  // Test 6: Appwrite Services Present
  console.log('\n📋 Test 6: Appwrite Services')
  const appwriteServices = [
    'src/lib/services/appwrite-storage-service.ts',
    'src/lib/services/appwrite-store-service.ts',
    'src/lib/services/appwrite-customer-service.ts',
    'src/lib/services/appwrite-order-service.ts',
    'src/hooks/use-appwrite-upload.ts',
    'src/lib/utils/image-utils.ts'
  ]

  let servicesPresent = 0
  appwriteServices.forEach(service => {
    if (existsSync(join(process.cwd(), service))) {
      servicesPresent++
    }
  })

  if (servicesPresent === appwriteServices.length) {
    console.log('✅ All Appwrite services present')
    results.passed++
  } else {
    console.log(`⚠️ ${servicesPresent}/${appwriteServices.length} Appwrite services present`)
    results.warnings++
  }

  // Test 7: Legacy Database Connection Disabled
  console.log('\n📋 Test 7: Legacy Database Connection')
  const dbFileExists = existsSync(join(process.cwd(), 'src/lib/db.ts'))
  const dbDisabledExists = existsSync(join(process.cwd(), 'src/lib/db.ts.disabled'))
  
  if (!dbFileExists && dbDisabledExists) {
    console.log('✅ Legacy database connection disabled')
    results.passed++
  } else if (dbFileExists) {
    console.log('⚠️ Legacy database connection still active')
    results.warnings++
  } else {
    console.log('❌ Database connection files missing')
    results.failed++
  }

  // Summary
  console.log('\n' + '='.repeat(50))
  console.log('📊 MIGRATION TEST SUMMARY')
  console.log('='.repeat(50))
  console.log(`✅ Passed: ${results.passed}`)
  console.log(`⚠️ Warnings: ${results.warnings}`)
  console.log(`❌ Failed: ${results.failed}`)
  
  const total = results.passed + results.warnings + results.failed
  const successRate = Math.round((results.passed / total) * 100)
  
  console.log(`\n🎯 Migration Success Rate: ${successRate}%`)
  
  if (results.failed === 0) {
    console.log('\n🎉 MIGRATION TESTS PASSED!')
    console.log('✅ Your application is ready for Appwrite-only operation')
    console.log('\n🚀 Next steps:')
    console.log('1. Start the development server: npm run dev')
    console.log('2. Register a new user: http://localhost:3001/auth/register')
    console.log('3. Test core functionality with Appwrite backend')
  } else {
    console.log('\n⚠️ MIGRATION INCOMPLETE')
    console.log('Some tests failed. Please review the issues above.')
  }
  
  if (results.warnings > 0) {
    console.log('\n💡 Note: Warnings indicate legacy fallbacks that are still active.')
    console.log('These provide backward compatibility but should be migrated eventually.')
  }
}

// Run the test
testMigrationStatus()
