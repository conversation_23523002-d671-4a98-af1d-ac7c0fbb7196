#!/usr/bin/env tsx

import { serverDatabases, DATABASE_ID, COLLECTIONS, convertToAppwriteDocument } from '../src/lib/appwrite-server'
import { ID } from 'node-appwrite'
import fs from 'fs/promises'
import path from 'path'

interface ImportData {
  storeCodes: any[]
  customers: any[]
  orders: any[]
  invoices: any[]
  invoiceItems: any[]
  storePricing: any[]
  storePricingTiers: any[]
  defaultPricing: any[]
  defaultPricingTiers: any[]
}

// Default user ID for migration (will be replaced with actual user IDs after auth is implemented)
const DEFAULT_USER_ID = 'migration-user'

async function importData(): Promise<void> {
  try {
    console.log('🚀 Starting Appwrite data import...')

    // Find the latest export file
    const exportsDir = path.join(process.cwd(), 'exports')
    const files = await fs.readdir(exportsDir)
    const simplifiedFiles = files.filter(f => f.startsWith('prisma-simplified-') && f.endsWith('.json'))
    
    if (simplifiedFiles.length === 0) {
      throw new Error('No export files found. Please run npm run db:export first.')
    }

    // Get the latest file
    const latestFile = simplifiedFiles.sort().reverse()[0]
    const filePath = path.join(exportsDir, latestFile)
    
    console.log(`📁 Reading export file: ${latestFile}`)
    const fileContent = await fs.readFile(filePath, 'utf-8')
    const data: ImportData = JSON.parse(fileContent)

    let totalImported = 0

    // Import Store Codes
    console.log('📦 Importing Store Codes...')
    const storeCodes = data.storeCodes || []
    for (const storeCode of storeCodes) {
      try {
        const appwriteData = convertToAppwriteDocument(storeCode, ['id', 'createdAt', 'updatedAt'])
        await serverDatabases.createDocument(
          DATABASE_ID,
          COLLECTIONS.STORES,
          ID.unique(),
          {
            ...appwriteData,
            userId: DEFAULT_USER_ID,
            parentStoreId: storeCode.parentStoreId ? String(storeCode.parentStoreId) : null
          }
        )
        totalImported++
        console.log(`  ✅ Imported store: ${storeCode.code}`)
      } catch (error: any) {
        console.error(`  ❌ Error importing store ${storeCode.code}:`, error.message)
      }
    }

    // Import Customers
    console.log('👥 Importing Customers...')
    const customers = data.customers || []
    for (const customer of customers) {
      try {
        const appwriteData = convertToAppwriteDocument(customer, ['id', 'createdAt', 'updatedAt'])
        await serverDatabases.createDocument(
          DATABASE_ID,
          COLLECTIONS.CUSTOMERS,
          ID.unique(),
          {
            ...appwriteData,
            userId: DEFAULT_USER_ID
          }
        )
        totalImported++
        console.log(`  ✅ Imported customer: ${customer.name}`)
      } catch (error: any) {
        console.error(`  ❌ Error importing customer ${customer.name}:`, error.message)
      }
    }

    // Import Orders
    console.log('📋 Importing Orders...')
    const orders = data.orders || []
    for (const order of orders) {
      try {
        const appwriteData = convertToAppwriteDocument(order, ['id', 'createdAt', 'updatedAt'])
        await serverDatabases.createDocument(
          DATABASE_ID,
          COLLECTIONS.ORDERS,
          ID.unique(),
          {
            ...appwriteData,
            userId: DEFAULT_USER_ID,
            storeCodeId: order.storeCodeId ? String(order.storeCodeId) : null,
            customerId: order.customerId ? String(order.customerId) : null,
            parentOrderId: order.parentOrderId ? String(order.parentOrderId) : null
          }
        )
        totalImported++
        console.log(`  ✅ Imported order: ${order.productName}`)
      } catch (error: any) {
        console.error(`  ❌ Error importing order ${order.productName}:`, error.message)
      }
    }

    // Import Invoices
    console.log('🧾 Importing Invoices...')
    const invoices = data.invoices || []
    for (const invoice of invoices) {
      try {
        const appwriteData = convertToAppwriteDocument(invoice, ['id', 'createdAt', 'updatedAt'])
        await serverDatabases.createDocument(
          DATABASE_ID,
          COLLECTIONS.INVOICES,
          ID.unique(),
          {
            ...appwriteData,
            userId: DEFAULT_USER_ID,
            customerId: String(invoice.customerId)
          }
        )
        totalImported++
        console.log(`  ✅ Imported invoice: ${invoice.invoiceNumber}`)
      } catch (error: any) {
        console.error(`  ❌ Error importing invoice ${invoice.invoiceNumber}:`, error.message)
      }
    }

    // Import Store Pricing
    console.log('💰 Importing Store Pricing...')
    const storePricing = data.storePricing || []
    for (const pricing of storePricing) {
      try {
        const appwriteData = convertToAppwriteDocument(pricing, ['id', 'createdAt', 'updatedAt'])
        await serverDatabases.createDocument(
          DATABASE_ID,
          COLLECTIONS.STORE_PRICING,
          ID.unique(),
          {
            ...appwriteData,
            userId: DEFAULT_USER_ID,
            storeCodeId: String(pricing.storeCodeId)
          }
        )
        totalImported++
        console.log(`  ✅ Imported store pricing: ${pricing.name}`)
      } catch (error: any) {
        console.error(`  ❌ Error importing store pricing ${pricing.name}:`, error.message)
      }
    }

    // Import Store Pricing Tiers
    console.log('📊 Importing Store Pricing Tiers...')
    const storePricingTiers = data.storePricingTiers || []
    for (const tier of storePricingTiers) {
      try {
        const appwriteData = convertToAppwriteDocument(tier, ['id', 'createdAt', 'updatedAt'])
        await serverDatabases.createDocument(
          DATABASE_ID,
          COLLECTIONS.PRICING_TIERS,
          ID.unique(),
          {
            ...appwriteData,
            userId: DEFAULT_USER_ID,
            storePricingId: String(tier.storePricingId)
          }
        )
        totalImported++
        console.log(`  ✅ Imported pricing tier: ${tier.minPrice} - ${tier.maxPrice || 'unlimited'}`)
      } catch (error: any) {
        console.error(`  ❌ Error importing pricing tier:`, error.message)
      }
    }

    console.log('🎉 Data import completed successfully!')
    console.log(`📊 Total records imported: ${totalImported}`)
    console.log('\n📋 Import Summary:')
    console.log(`  - Store Codes: ${storeCodes.length}`)
    console.log(`  - Customers: ${customers.length}`)
    console.log(`  - Orders: ${orders.length}`)
    console.log(`  - Invoices: ${invoices.length}`)
    console.log(`  - Store Pricing: ${storePricing.length}`)
    console.log(`  - Store Pricing Tiers: ${storePricingTiers.length}`)

    console.log('\n⚠️  Important Notes:')
    console.log('  - All data has been imported with a default user ID')
    console.log('  - After implementing authentication, you will need to:')
    console.log('    1. Create user accounts')
    console.log('    2. Update all records with proper user IDs')
    console.log('    3. Set up proper permissions')

  } catch (error) {
    console.error('❌ Error importing data:', error)
    process.exit(1)
  }
}

// Run the import
importData()
