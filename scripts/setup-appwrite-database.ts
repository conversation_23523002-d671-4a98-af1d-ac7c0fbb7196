#!/usr/bin/env tsx

import { serverClient, serverDatabases } from '../src/lib/appwrite-server'
import { DATABASE_ID, COLLECTIONS } from '../src/lib/appwrite'
import { ID } from 'node-appwrite'

interface AttributeConfig {
  key: string
  type: 'string' | 'integer' | 'float' | 'boolean' | 'datetime' | 'email' | 'url' | 'enum'
  size?: number
  required?: boolean
  default?: any
  array?: boolean
  enumValues?: string[]
}

interface CollectionConfig {
  id: string
  name: string
  attributes: AttributeConfig[]
  indexes?: Array<{
    key: string
    type: 'key' | 'fulltext' | 'unique'
    attributes: string[]
    orders?: string[]
  }>
}

const collections: CollectionConfig[] = [
  {
    id: COLLECTIONS.STORES,
    name: 'Store Codes',
    attributes: [
      { key: 'code', type: 'string', size: 10, required: true },
      { key: 'name', type: 'string', size: 255, required: false },
      { key: 'description', type: 'string', size: 1000, required: false },
      { key: 'address', type: 'string', size: 500, required: false },
      { key: 'city', type: 'string', size: 100, required: false },
      { key: 'state', type: 'string', size: 100, required: false },
      { key: 'postalCode', type: 'string', size: 20, required: false },
      { key: 'country', type: 'string', size: 100, required: false, default: 'Philippines' },
      { key: 'phone', type: 'string', size: 50, required: false },
      { key: 'email', type: 'email', required: false },
      { key: 'website', type: 'url', required: false },
      { key: 'managerName', type: 'string', size: 255, required: false },
      { key: 'managerEmail', type: 'email', required: false },
      { key: 'contactPerson', type: 'string', size: 255, required: false },
      { key: 'operatingHours', type: 'string', size: 255, required: false },
      { key: 'timezone', type: 'string', size: 50, required: false, default: 'Asia/Manila' },
      { key: 'isOpen', type: 'boolean', required: false, default: true },
      { key: 'allowsPickup', type: 'boolean', required: false, default: true },
      { key: 'allowsDelivery', type: 'boolean', required: false, default: true },
      { key: 'deliveryRadius', type: 'float', required: false },
      { key: 'minimumOrder', type: 'float', required: false, default: 0.00 },
      { key: 'serviceFee', type: 'float', required: false, default: 0.00 },
      { key: 'averageProcessingTime', type: 'integer', required: false },
      { key: 'capacity', type: 'integer', required: false },
      { key: 'priority', type: 'enum', enumValues: ['LOW', 'NORMAL', 'HIGH', 'URGENT'], required: false, default: 'NORMAL' },
      { key: 'totalOrders', type: 'integer', required: false, default: 0 },
      { key: 'totalRevenue', type: 'float', required: false, default: 0.00 },
      { key: 'averageOrderValue', type: 'float', required: false, default: 0.00 },
      { key: 'notes', type: 'string', size: 2000, required: false },
      { key: 'internalNotes', type: 'string', size: 2000, required: false },
      { key: 'specialInstructions', type: 'string', size: 2000, required: false },
      { key: 'externalStoreId', type: 'string', size: 255, required: false },
      { key: 'apiEndpoint', type: 'url', required: false },
      { key: 'apiKey', type: 'string', size: 255, required: false },
      { key: 'parentStoreId', type: 'string', size: 255, required: false },
      { key: 'userId', type: 'string', size: 255, required: true }
    ],
    indexes: [
      { key: 'code_unique', type: 'unique', attributes: ['code', 'userId'] },
      { key: 'user_stores', type: 'key', attributes: ['userId'] },
      { key: 'store_search', type: 'fulltext', attributes: ['code', 'name'] }
    ]
  },
  {
    id: COLLECTIONS.CUSTOMERS,
    name: 'Customers',
    attributes: [
      { key: 'name', type: 'string', size: 255, required: true },
      { key: 'customerNumber', type: 'string', size: 100, required: false },
      { key: 'customerType', type: 'enum', enumValues: ['INDIVIDUAL', 'BUSINESS'], required: false, default: 'INDIVIDUAL' },
      { key: 'status', type: 'enum', enumValues: ['ACTIVE', 'INACTIVE', 'SUSPENDED'], required: false, default: 'ACTIVE' },
      { key: 'email', type: 'email', required: false },
      { key: 'phone', type: 'string', size: 50, required: false },
      { key: 'alternatePhone', type: 'string', size: 50, required: false },
      { key: 'website', type: 'url', required: false },
      { key: 'address', type: 'string', size: 500, required: false },
      { key: 'city', type: 'string', size: 100, required: false },
      { key: 'state', type: 'string', size: 100, required: false },
      { key: 'postalCode', type: 'string', size: 20, required: false },
      { key: 'country', type: 'string', size: 100, required: false, default: 'Philippines' },
      { key: 'businessName', type: 'string', size: 255, required: false },
      { key: 'taxId', type: 'string', size: 100, required: false },
      { key: 'businessType', type: 'string', size: 100, required: false },
      { key: 'preferredDeliveryMethod', type: 'string', size: 100, required: false },
      { key: 'preferredPaymentMethod', type: 'string', size: 100, required: false },
      { key: 'creditLimit', type: 'float', required: false, default: 0.00 },
      { key: 'paymentTerms', type: 'integer', required: false, default: 30 },
      { key: 'discountRate', type: 'float', required: false, default: 0.00 },
      { key: 'segment', type: 'enum', enumValues: ['REGULAR', 'VIP', 'PREMIUM'], required: false, default: 'REGULAR' },
      { key: 'loyaltyTier', type: 'enum', enumValues: ['BRONZE', 'SILVER', 'GOLD', 'PLATINUM'], required: false, default: 'BRONZE' },
      { key: 'loyaltyPoints', type: 'integer', required: false, default: 0 },
      { key: 'assignedSalesRep', type: 'string', size: 255, required: false },
      { key: 'accountManager', type: 'string', size: 255, required: false },
      { key: 'referredBy', type: 'string', size: 255, required: false },
      { key: 'firstOrderDate', type: 'datetime', required: false },
      { key: 'lastOrderDate', type: 'datetime', required: false },
      { key: 'lastContactDate', type: 'datetime', required: false },
      { key: 'totalOrders', type: 'integer', required: false, default: 0 },
      { key: 'totalSpent', type: 'float', required: false, default: 0.00 },
      { key: 'averageOrderValue', type: 'float', required: false, default: 0.00 },
      { key: 'notes', type: 'string', size: 2000, required: false },
      { key: 'internalNotes', type: 'string', size: 2000, required: false },
      { key: 'userId', type: 'string', size: 255, required: true }
    ],
    indexes: [
      { key: 'customer_name_unique', type: 'unique', attributes: ['name', 'userId'] },
      { key: 'user_customers', type: 'key', attributes: ['userId'] },
      { key: 'customer_search', type: 'fulltext', attributes: ['name', 'email', 'phone'] },
      { key: 'customer_status', type: 'key', attributes: ['status'] }
    ]
  },
  {
    id: COLLECTIONS.ORDERS,
    name: 'Orders',
    attributes: [
      // Core product fields
      { key: 'productName', type: 'string', size: 255, required: true },
      { key: 'quantity', type: 'integer', required: false, default: 1 },
      { key: 'usageUnit', type: 'string', size: 50, required: false },
      { key: 'comment', type: 'string', size: 1000, required: false },
      { key: 'imageFilename', type: 'string', size: 255, required: false },

      // Pricing fields
      { key: 'storePrice', type: 'float', required: false, default: 0.00 },
      { key: 'pasabuyFee', type: 'float', required: false, default: 0.00 },
      { key: 'customerPrice', type: 'float', required: false, default: 0.00 },

      // Status fields
      { key: 'isBought', type: 'boolean', required: false, default: false },
      { key: 'packingStatus', type: 'enum', enumValues: ['Not Packed', 'Packed', 'Shipped'], required: false, default: 'Not Packed' },

      // Essential metadata
      { key: 'orderNumber', type: 'string', size: 100, required: false },
      { key: 'category', type: 'string', size: 100, required: false },
      { key: 'priority', type: 'enum', enumValues: ['LOW', 'NORMAL', 'HIGH', 'URGENT'], required: false, default: 'NORMAL' },

      // Relationships
      { key: 'storeCodeId', type: 'string', size: 255, required: false },
      { key: 'customerId', type: 'string', size: 255, required: false },
      { key: 'userId', type: 'string', size: 255, required: true }
    ],
    indexes: [
      { key: 'user_orders', type: 'key', attributes: ['userId'] },
      { key: 'order_search', type: 'fulltext', attributes: ['productName', 'orderNumber'] },
      { key: 'order_status', type: 'key', attributes: ['isBought', 'packingStatus'] },
      { key: 'store_orders', type: 'key', attributes: ['storeCodeId'] },
      { key: 'customer_orders', type: 'key', attributes: ['customerId'] }
    ]
  },
  {
    id: COLLECTIONS.INVOICES,
    name: 'Invoices',
    attributes: [
      // Core invoice fields
      { key: 'invoiceNumber', type: 'string', size: 100, required: true },
      { key: 'customerId', type: 'string', size: 255, required: true },
      { key: 'status', type: 'enum', enumValues: ['DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED'], required: false, default: 'DRAFT' },

      // Financial fields
      { key: 'subtotal', type: 'float', required: false, default: 0.00 },
      { key: 'taxAmount', type: 'float', required: false, default: 0.00 },
      { key: 'total', type: 'float', required: false, default: 0.00 },
      { key: 'currency', type: 'string', size: 10, required: false, default: 'PHP' },

      // Date fields
      { key: 'issueDate', type: 'datetime', required: false },
      { key: 'dueDate', type: 'datetime', required: false },
      { key: 'paidDate', type: 'datetime', required: false },

      // Essential metadata
      { key: 'paymentMethod', type: 'string', size: 100, required: false },
      { key: 'notes', type: 'string', size: 2000, required: false },
      { key: 'userId', type: 'string', size: 255, required: true }
    ],
    indexes: [
      { key: 'invoice_number_unique', type: 'unique', attributes: ['invoiceNumber', 'userId'] },
      { key: 'user_invoices', type: 'key', attributes: ['userId'] },
      { key: 'customer_invoices', type: 'key', attributes: ['customerId'] },
      { key: 'invoice_status', type: 'key', attributes: ['status'] },
      { key: 'invoice_dates', type: 'key', attributes: ['issueDate', 'dueDate'] }
    ]
  },
  {
    id: 'store_pricing_rules',
    name: 'Store Pricing Rules',
    attributes: [
      { key: 'storeCodeId', type: 'string', size: 255, required: true },
      { key: 'storeName', type: 'string', size: 255, required: false },
      { key: 'itemId', type: 'string', size: 255, required: false },
      { key: 'itemName', type: 'string', size: 255, required: false },
      { key: 'category', type: 'string', size: 100, required: false },
      { key: 'price', type: 'float', required: true },
      { key: 'originalPrice', type: 'float', required: false },
      { key: 'cost', type: 'float', required: false },
      { key: 'margin', type: 'float', required: false },
      { key: 'discountPercentage', type: 'float', required: false },
      { key: 'discountAmount', type: 'float', required: false },
      { key: 'validFrom', type: 'string', size: 50, required: false },
      { key: 'validTo', type: 'string', size: 50, required: false },
      { key: 'notes', type: 'string', size: 2000, required: false },
      { key: 'isEnabled', type: 'boolean', required: false, default: true },
      { key: 'userId', type: 'string', size: 255, required: true }
    ],
    indexes: [
      { key: 'user_pricing_rules', type: 'key', attributes: ['userId'] },
      { key: 'store_pricing', type: 'key', attributes: ['storeCodeId'] },
      { key: 'pricing_search', type: 'fulltext', attributes: ['storeName', 'itemName'] }
    ]
  },
  {
    id: 'pricing_settings',
    name: 'Pricing Settings',
    attributes: [
      { key: 'name', type: 'string', size: 255, required: true },
      { key: 'serviceFee', type: 'float', required: false, default: 0.00 },
      { key: 'isDefault', type: 'boolean', required: false, default: false },
      { key: 'userId', type: 'string', size: 255, required: true }
    ],
    indexes: [
      { key: 'user_pricing_settings', type: 'key', attributes: ['userId'] },
      { key: 'default_settings', type: 'key', attributes: ['isDefault'] }
    ]
  },
  {
    id: 'filter_presets',
    name: 'Filter Presets',
    attributes: [
      { key: 'name', type: 'string', size: 255, required: true },
      { key: 'filters', type: 'string', size: 2000, required: false },
      { key: 'isDefault', type: 'boolean', required: false, default: false },
      { key: 'userId', type: 'string', size: 255, required: true }
    ],
    indexes: [
      { key: 'user_filter_presets', type: 'key', attributes: ['userId'] },
      { key: 'default_presets', type: 'key', attributes: ['isDefault'] }
    ]
  }
]

async function createDatabase() {
  try {
    console.log('🚀 Creating Appwrite database...')
    
    // Create database
    try {
      await serverDatabases.create(DATABASE_ID, 'PasaBuy Pal Database')
      console.log('✅ Database created successfully')
    } catch (error: any) {
      if (error.code === 409) {
        console.log('ℹ️ Database already exists, continuing...')
      } else {
        throw error
      }
    }

    // Create collections
    for (const collection of collections) {
      console.log(`📁 Creating collection: ${collection.name}`)
      
      try {
        await serverDatabases.createCollection(
          DATABASE_ID,
          collection.id,
          collection.name,
          undefined, // permissions - will be set per document
          false, // documentSecurity
          true // enabled
        )
        console.log(`✅ Collection ${collection.name} created`)
      } catch (error: any) {
        if (error.code === 409) {
          console.log(`ℹ️ Collection ${collection.name} already exists`)
        } else {
          throw error
        }
      }

      // Create attributes
      for (const attr of collection.attributes) {
        console.log(`  📝 Creating attribute: ${attr.key}`)
        
        try {
          switch (attr.type) {
            case 'string':
              await serverDatabases.createStringAttribute(
                DATABASE_ID,
                collection.id,
                attr.key,
                attr.size || 255,
                attr.required || false,
                attr.default,
                attr.array || false
              )
              break
            case 'integer':
              await serverDatabases.createIntegerAttribute(
                DATABASE_ID,
                collection.id,
                attr.key,
                attr.required || false,
                undefined, // min
                undefined, // max
                attr.default,
                attr.array || false
              )
              break
            case 'float':
              await serverDatabases.createFloatAttribute(
                DATABASE_ID,
                collection.id,
                attr.key,
                attr.required || false,
                undefined, // min
                undefined, // max
                attr.default,
                attr.array || false
              )
              break
            case 'boolean':
              await serverDatabases.createBooleanAttribute(
                DATABASE_ID,
                collection.id,
                attr.key,
                attr.required || false,
                attr.default,
                attr.array || false
              )
              break
            case 'datetime':
              await serverDatabases.createDatetimeAttribute(
                DATABASE_ID,
                collection.id,
                attr.key,
                attr.required || false,
                attr.default,
                attr.array || false
              )
              break
            case 'email':
              await serverDatabases.createEmailAttribute(
                DATABASE_ID,
                collection.id,
                attr.key,
                attr.required || false,
                attr.default,
                attr.array || false
              )
              break
            case 'url':
              await serverDatabases.createUrlAttribute(
                DATABASE_ID,
                collection.id,
                attr.key,
                attr.required || false,
                attr.default,
                attr.array || false
              )
              break
            case 'enum':
              await serverDatabases.createEnumAttribute(
                DATABASE_ID,
                collection.id,
                attr.key,
                attr.enumValues || [],
                attr.required || false,
                attr.default,
                attr.array || false
              )
              break
          }
          console.log(`    ✅ Attribute ${attr.key} created`)
        } catch (error: any) {
          if (error.code === 409) {
            console.log(`    ℹ️ Attribute ${attr.key} already exists`)
          } else {
            console.error(`    ❌ Error creating attribute ${attr.key}:`, error.message)
          }
        }
      }

      // Wait a bit for attributes to be ready
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    console.log('🎉 Database setup completed successfully!')
    
  } catch (error) {
    console.error('❌ Error setting up database:', error)
    process.exit(1)
  }
}

// Run the setup
createDatabase()
