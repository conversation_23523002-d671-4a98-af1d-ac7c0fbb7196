#!/usr/bin/env tsx

import { serverUsers } from '../src/lib/appwrite-server'
import { ID } from 'node-appwrite'

async function testUserAccount() {
  console.log('🔐 Testing user account: <EMAIL>')
  
  const testEmail = '<EMAIL>'
  const testPassword = 'Password01'
  const testName = 'Test User'
  
  try {
    // Check if user already exists
    console.log('\n📋 Checking existing users...')
    const users = await serverUsers.list()
    console.log(`Found ${users.total} existing users`)
    
    const existingUser = users.users.find(user => user.email === testEmail)
    
    if (existingUser) {
      console.log(`✅ Test user already exists: ${existingUser.name} (${existingUser.email})`)
      console.log(`   User ID: ${existingUser.$id}`)
      console.log(`   Status: ${existingUser.status ? 'Active' : 'Inactive'}`)
      console.log(`   Email Verified: ${existingUser.emailVerification}`)
    } else {
      console.log('📝 Creating test user...')
      
      try {
        const newUser = await serverUsers.create(
          ID.unique(),
          testEmail,
          undefined, // phone (optional)
          testPassword,
          testName
        )
        
        console.log(`✅ Test user created successfully!`)
        console.log(`   Name: ${newUser.name}`)
        console.log(`   Email: ${newUser.email}`)
        console.log(`   User ID: ${newUser.$id}`)
        
        // Set email as verified for testing
        await serverUsers.updateEmailVerification(newUser.$id, true)
        console.log(`✅ Email verification set to true`)
        
      } catch (createError: any) {
        console.error(`❌ Failed to create user: ${createError.message}`)
        throw createError
      }
    }
    
    console.log('\n🎉 Test account ready!')
    console.log('\n📋 You can now:')
    console.log('  1. Start your dev server: npm run dev')
    console.log('  2. Visit: http://localhost:3001/auth/login')
    console.log('  3. Login with:')
    console.log(`     Email: ${testEmail}`)
    console.log(`     Password: ${testPassword}`)
    console.log('  4. Test the application functionality')
    
  } catch (error: any) {
    console.error('❌ Test account setup failed:', error.message)
    console.error('Full error:', error)
    process.exit(1)
  }
}

// Run the test
testUserAccount()
